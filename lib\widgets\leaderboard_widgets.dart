import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/profile_photo.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RankHeaderWidget extends StatefulWidget {
  final bool? isOnResult;
  final bool? isOnLeaderboard;
  const RankHeaderWidget({super.key, this.isOnResult, this.isOnLeaderboard});

  @override
  State<RankHeaderWidget> createState() => _RankHeaderWidgetState();
}

class _RankHeaderWidgetState extends State<RankHeaderWidget> {
  final profileController = Get.find<ProfileController>();

  String get _userId => FirebaseAuth.instance.currentUser?.uid ?? '';

  @override
  Widget build(BuildContext context) {
    if (_userId.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(color: mainColor),
      );
    }

    return StreamBuilder<DocumentSnapshot>(
      stream: FirebaseFirestore.instance
          .collection('users')
          .doc(_userId)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting ||
            snapshot.hasError ||
            !snapshot.hasData ||
            snapshot.data?.data() == null) {
          return const Center(
            child: CircularProgressIndicator(color: mainColor),
          );
        }

        var userData = snapshot.data!.data() as Map<String, dynamic>;
        String title = userData['title'] ?? 'No Title';
        String league = userData['league'] ?? 'No League';
        int neurons = userData['neurons'] ?? 0;

        return Container(
          decoration: BoxDecoration(
            color: const Color(0xffF8EFFF),
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(
              color: const Color(0xff9610FF).withAlpha(128),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    ProfilePhotoWidget(
                      isOnSettings: true,
                      isOnResult: widget.isOnResult,
                    ),
                    const SizedBox(width: 16.0),
                    const SizedBox(
                      height: 80, // Same height as profile photo
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Txt(txt: 'Titula', fontSize: 14),
                          Txt(txt: 'Liga', fontSize: 14),
                          Txt(txt: 'Neuroni', fontSize: 14),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: SizedBox(
                        height: 80, // Same height as profile photo
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Txt(
                              txt: title,
                              fontSize: 14,
                              minFontSize: 14,
                              maxLines: 1,
                              fontColor: grey2Color,
                            ),
                            Txt(
                              txt: 'Liga $league',
                              fontSize: 14,
                              minFontSize: 14,
                              maxLines: 1,
                              fontColor: grey2Color,
                            ),
                            Txt(
                              txt: neurons.toString(),
                              fontSize: 14,
                              minFontSize: 14,
                              maxLines: 1,
                              fontColor: grey2Color,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                if (widget.isOnResult!) ...[
                  const SizedBox(height: 12),
                  const Txt(
                    txt:
                        'Sjajan posao do sada! Samo nastavi da daješ sve od sebe i briljiraš na svom pohodu ka vrhu!',
                    fontSize: 12,
                    maxLines: 3,
                    fontColor: grey2Color,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
