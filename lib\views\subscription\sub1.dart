import 'package:bibl/controllers/profile_controller.dart';
import 'package:flutter/material.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import '../../controllers/analytics_controller.dart';
import '../../controllers/payment_controller.dart';
import '../../res/style.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class SubscriptionScreen1 extends StatefulWidget {
  final bool? isBackButton;
  const SubscriptionScreen1({super.key, this.isBackButton});

  @override
  State<SubscriptionScreen1> createState() => _SubscriptionScreen1State();
}

class _SubscriptionScreen1State extends State<SubscriptionScreen1> {
  final ProfileController profileController = Get.find();
  final AnalticsController analticsController = AnalticsController();

  List<Offering> offerings = [];
  Package? selectedPackage;
  bool isFetchingOffers = false;
  bool isMonthlySelected = true;
  bool isPurchasing = false;
  bool hasInternet = true;

  // Trial eligibility
  bool? monthlyTrialEligible;
  bool? yearlyTrialEligible;

  @override
  void initState() {
    super.initState();
    analticsController.subPageDisplayedAnalyticsUpdate('SubscriptionScreen');
    _initializeSubscription();
    _checkConnectivity();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    setState(() {
      hasInternet = !connectivityResult.contains(ConnectivityResult.none);
    });

    // Listen to connectivity changes
    Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
      if (mounted) {
        setState(() {
          hasInternet = !results.contains(ConnectivityResult.none);
        });
        if (hasInternet && offerings.isEmpty) {
          fetchOffers();
        }
      }
    });
  }

  Future<void> _initializeSubscription() async {
    // Initialize RevenueCat if not already done
    if (!PurchaseApi.isInitialized.value) {
      final success = await PurchaseApi.init();
      if (!success) {
        if (mounted) {
          getErrorSnackBar('Greška pri inicijalizaciji sistema za plaćanje');
        }
        return;
      }
    }

    // Check current subscription status
    await PurchaseApi.checkSubscriptionStatus();

    // Fetch available offers
    await fetchOffers();
  }

  Future<void> fetchOffers() async {
    if (!hasInternet) {
      getErrorSnackBar('Nema internet konekcije');
      return;
    }

    setState(() => isFetchingOffers = true);

    try {
      offerings = await PurchaseApi.fetchOffers();

      if (offerings.isNotEmpty &&
          offerings.first.availablePackages.isNotEmpty) {
        // Find and set the appropriate package based on selected tab
        _updateSelectedPackage();

        // Check trial eligibility for both packages
        await _checkTrialEligibility();
      }
    } catch (e) {
      if (mounted) {
        getErrorSnackBar('Greška pri učitavanju ponuda');
      }
    } finally {
      if (mounted) {
        setState(() => isFetchingOffers = false);
      }
    }
  }

  void _updateSelectedPackage() {
    if (offerings.isEmpty || offerings.first.availablePackages.isEmpty) {
      selectedPackage = null;
      return;
    }

    final packages = offerings.first.availablePackages;

    // Find the package based on billing period
    selectedPackage = packages.firstWhereOrNull((package) {
      final period = package.storeProduct.defaultOption?.billingPeriod?.iso8601;
      return isMonthlySelected ? period == 'P1M' : period == 'P1Y';
    });

    // Fallback to first available package if no match found
    selectedPackage ??= packages.first;
  }

  Future<void> _checkTrialEligibility() async {
    if (offerings.isEmpty) return;

    final packages = offerings.first.availablePackages;

    for (var package in packages) {
      final period = package.storeProduct.defaultOption?.billingPeriod?.iso8601;
      final eligible = await PurchaseApi.checkTrialEligibility(package);

      if (period == 'P1M') {
        monthlyTrialEligible = eligible;
      } else if (period == 'P1Y') {
        yearlyTrialEligible = eligible;
      }
    }

    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  _buildHeader(),
                  const SizedBox(height: 16.0),
                  _buildTabs(),
                  const SizedBox(height: 16.0),
                  _buildTitle(),
                  const Spacer(),
                  _buildContent(),
                  Spacer(
                      flex: MediaQuery.of(context).size.height < 700 ? 1 : 4),
                  const Spacer(),
                  _buildBottomSection(),
                ],
              ),
            ),
            if (isPurchasing) _buildLoadingOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (widget.isBackButton == true)
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Get.back(),
          )
        else
          const SizedBox(width: 48),

        // Show current subscription status
        if (PurchaseApi.hasActiveSubscription.value)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.green),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.check_circle, color: Colors.green, size: 16),
                const SizedBox(width: 4),
                Txt(
                  txt: PurchaseApi.subscriptionType.value == 'monthly'
                      ? 'Mesečna pretplata'
                      : 'Godišnja pretplata',
                  fontSize: 12,
                  fontColor: Colors.green,
                ),
              ],
            ),
          ),

        // Restore purchases button
        TextButton(
          onPressed: _handleRestorePurchases,
          child: const Txt(
            txt: 'Vrati kupovine',
            fontSize: 14,
            fontColor: mainColor,
          ),
        ),
      ],
    );
  }

  Widget _buildTabs() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xff747480).withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(100),
      ),
      child: Padding(
        padding: const EdgeInsets.all(5.0),
        child: Row(
          children: [
            _buildTab('Mesečno', isMonthlySelected, () {
              setState(() {
                isMonthlySelected = true;
                _updateSelectedPackage();
              });
            }),
            _buildTab('Godišnje', !isMonthlySelected, () {
              setState(() {
                isMonthlySelected = false;
                _updateSelectedPackage();
              });
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildTab(String text, bool isSelected, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: isSelected ? Colors.white : null,
            borderRadius: BorderRadius.circular(30.0),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ]
                : null,
          ),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Txt(
                txt: text,
                fontSize: 14,
                fontColor: isSelected
                    ? Colors.black
                    : const Color(0xff3C3C43).withValues(alpha: 0.6),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Txt(
      txt: 'Znanje je jedino što ti niko ne može oduzeti',
      fontSize: _getResponsiveFontSize(),
      maxLines: 5,
      fontColor: const Color(0xff211F20),
      textAlign: TextAlign.center,
      fontWeight: FontWeight.bold,
    );
  }

  double _getResponsiveFontSize() {
    final height = MediaQuery.of(context).size.height;
    if (height < 700) return 18;
    if (height < 800) return 24;
    if (height < 900) return 28;
    return 32;
  }

  Widget _buildContent() {
    if (!hasInternet) {
      return _buildNoInternetWidget();
    }

    if (isFetchingOffers) {
      return _buildLoadingWidget();
    }

    if (offerings.isEmpty) {
      return _buildNoOffersWidget();
    }

    // Show subscription management if already subscribed
    if (PurchaseApi.hasActiveSubscription.value) {
      return _buildActiveSubscriptionWidget();
    }

    return _buildOfferingsWidget();
  }

  Widget _buildNoInternetWidget() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.wifi_off,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Txt(
            txt: 'Nema internet konekcije',
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(height: 8),
          const Txt(
            txt: 'Molimo proverite vašu konekciju i pokušajte ponovo',
            fontSize: 14,
            fontColor: Colors.grey,
            textAlign: TextAlign.center,
            maxLines: 2,
          ),
          const SizedBox(height: 16),
          buttonContainer(
            onTap: () => fetchOffers(),
            text: 'Pokušaj ponovo',
            height: 40,
            width: 150,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(color: mainColor),
          SizedBox(height: 16),
          Txt(
            txt: 'Učitavanje ponuda...',
            fontSize: 14,
            fontColor: Colors.grey,
          ),
        ],
      ),
    );
  }

  Widget _buildNoOffersWidget() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          SizedBox(height: Get.height * 0.1),
          const Txt(
            txt: 'Trenutno nema dostupnih pretplata',
            maxLines: 2,
            textAlign: TextAlign.center,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(height: 8),
          const Txt(
            txt: 'Molimo pokušajte ponovo kasnije',
            fontSize: 14,
            fontColor: Colors.grey,
          ),
          const SizedBox(height: 16),
          buttonContainer(
            onTap: () => fetchOffers(),
            text: 'Osvježi',
            height: 40,
            width: 120,
          ),
        ],
      ),
    );
  }

  Widget _buildActiveSubscriptionWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Txt(
            txt: 'Već imate aktivnu pretplatu!',
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(height: 8),
          Txt(
            txt: PurchaseApi.subscriptionType.value == 'monthly'
                ? 'Mesečna pretplata'
                : 'Godišnja pretplata',
            fontSize: 16,
            fontColor: Colors.grey[700],
          ),
          if (PurchaseApi.subscriptionExpiryDate.value.isNotEmpty) ...[
            const SizedBox(height: 8),
            Txt(
              txt:
                  'Važi do: ${_formatDate(PurchaseApi.subscriptionExpiryDate.value)}',
              fontSize: 14,
              fontColor: Colors.grey,
            ),
          ],
          if (PurchaseApi.isInTrialPeriod.value) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Txt(
                txt: 'Probni period',
                fontSize: 12,
                fontColor: Colors.blue,
              ),
            ),
          ],
          const SizedBox(height: 16),
          TextButton(
            onPressed: _handleManageSubscription,
            child: const Txt(
              txt: 'Upravljaj pretplatom',
              fontSize: 14,
              fontColor: mainColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      return '${date.day}.${date.month}.${date.year}';
    } catch (e) {
      return dateStr;
    }
  }

  Widget _buildOfferingsWidget() {
    return SizedBox(
      height: MediaQuery.of(context).size.height < 700 ? 250 : 300,
      width: Get.width,
      child: Stack(
        children: [
          const ColorfulSmokyEffect(),
          _buildPriceWidget(),
        ],
      ),
    );
  }

  Widget _buildPriceWidget() {
    if (selectedPackage == null) return const SizedBox();

    final hasTrialEligibility = isMonthlySelected
        ? (monthlyTrialEligible ?? false)
        : (yearlyTrialEligible ?? false);

    return Align(
      alignment: Alignment.center,
      child: SizedBox(
        height: MediaQuery.of(context).size.height < 700 ? 220 : 270,
        width: Get.width * 0.9,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.center,
              child: Container(
                height: MediaQuery.of(context).size.height < 700 ? 180 : 220,
                width: Get.width * 0.85,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFFED6A0B).withValues(alpha: 0.32),
                      const Color(0xFFAF52DE).withValues(alpha: 0.32),
                    ],
                    stops: const [0.1, 0.9],
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        _buildPackageDetails(hasTrialEligibility),
                        const SizedBox(height: 8),
                        _buildPackageInfo(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            ..._buildStarDecorations(),
          ],
        ),
      ),
    );
  }

  Widget _buildPackageDetails(bool hasTrialEligibility) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(50),
                color: isMonthlySelected
                    ? const Color(0xff4F9D17).withValues(alpha: 0.08)
                    : const Color(0xffAF52DE).withValues(alpha: 0.08),
              ),
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 3, 16, 3),
                child: Txt(
                  txt: isMonthlySelected ? 'Popularno' : 'Najpovoljnije',
                  fontColor: isMonthlySelected
                      ? const Color(0xff4F9D17)
                      : const Color(0xffAF52DE),
                  textAlign: TextAlign.center,
                  font: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: MediaQuery.of(context).size.height < 700 ? 10 : 13,
                ),
              ),
            ),
            if (hasTrialEligibility) ...[
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50),
                  color: Colors.blue.withValues(alpha: 0.08),
                ),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(12, 3, 12, 3),
                  child: Txt(
                    txt: 'Probni period',
                    fontColor: Colors.blue,
                    textAlign: TextAlign.center,
                    font: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize:
                        MediaQuery.of(context).size.height < 700 ? 10 : 13,
                  ),
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        const Txt(
          txt: 'umniLab Plus',
          fontSize: 20,
          font: 'Inter',
          fontWeight: FontWeight.w600,
        ),
      ],
    );
  }

  Widget _buildPackageInfo() {
    if (selectedPackage == null) return const SizedBox();

    final isYearly =
        selectedPackage!.storeProduct.defaultOption?.billingPeriod?.iso8601 ==
            'P1Y';
    final currencyCode = selectedPackage!.storeProduct.currencyCode;
    final price = selectedPackage!.storeProduct.price;

    // Check for trial info
    final introPrice = selectedPackage!.storeProduct.introductoryPrice;
    final hasTrialEligibility = isMonthlySelected
        ? (monthlyTrialEligible ?? false)
        : (yearlyTrialEligible ?? false);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Txt(
          txt:
              'Uživaj u dodatnom premium sadržaju bez reklama, stekni znanje za manje utrošenog vremena, otkažite bilo kad.',
          maxLines: 10,
          fontWeight: FontWeight.w600,
          font: 'Inter',
          fontSize: MediaQuery.of(context).size.height < 700 ? 12 : 15,
          fontColor: const Color(0xff211F20).withValues(alpha: 0.65),
        ),
        const SizedBox(height: 8),

        // Show trial info if available
        if (hasTrialEligibility && introPrice != null) ...[
          Txt(
            txt: _getTrialText(introPrice),
            maxLines: 2,
            fontWeight: FontWeight.bold,
            font: 'Inter',
            fontSize: MediaQuery.of(context).size.height < 700 ? 12 : 15,
            fontColor: Colors.blue,
          ),
          const SizedBox(height: 4),
        ],

        Txt(
          txt: isYearly
              ? 'Samo $currencyCode${price.toStringAsFixed(0)}/Godišnje'
              : 'Samo $currencyCode${price.toStringAsFixed(0)}/Mesečno',
          maxLines: 2,
          fontWeight: FontWeight.w600,
          font: 'Inter',
          fontSize: MediaQuery.of(context).size.height < 700 ? 12 : 15,
          fontColor: const Color(0xff211F20).withValues(alpha: 0.65),
        ),
      ],
    );
  }

  String _getTrialText(IntroductoryPrice introPrice) {
    // For now, return a simple trial text since the API structure might be different
    // You can customize this based on your specific trial periods
    return 'Probni period dostupan';
  }

  List<Widget> _buildStarDecorations() {
    return [
      _buildStarDecoration(top: -15, right: 120, size: 50, blurRadius: 80),
      _buildStarDecoration(top: -10, right: 80, size: 80, blurRadius: 80),
      _buildStarDecoration(top: 50, right: -26, size: 80, blurRadius: 80),
    ];
  }

  Widget _buildStarDecoration({
    required double top,
    required double right,
    required double size,
    required double blurRadius,
  }) {
    return Positioned(
      top: top,
      right: right,
      child: Container(
        height: size,
        width: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.yellow.withValues(alpha: 0.3),
              blurRadius: blurRadius,
              offset: const Offset(0, 0),
            ),
          ],
        ),
        child: SvgPicture.asset(
          'assets/svgs/star_icon.svg',
          height: size,
        ),
      ),
    );
  }

  Widget _buildBottomSection() {
    return Column(
      children: [
        _buildContinueButton(),
        const SizedBox(height: 12),
        _buildLegalText(),
      ],
    );
  }

  Widget _buildContinueButton() {
    return buttonContainer(
      height: MediaQuery.of(context).size.height < 700 ? 50 : null,
      onTap: _handlePurchase,
      text: 'Nastavi',
    );
  }

  Widget _buildLegalText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          const Txt(
            txt:
                'Pretplatom prihvatate naše Uslove korišćenja i Politiku privatnosti',
            fontSize: 11,
            fontColor: Colors.grey,
            textAlign: TextAlign.center,
            maxLines: 2,
          ),
          const SizedBox(height: 4),
          if (PurchaseApi.isInGracePeriod.value)
            const Txt(
              txt:
                  'Vaša pretplata je u grace periodu. Molimo ažurirajte način plaćanja.',
              fontSize: 11,
              fontColor: Colors.orange,
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.5),
      child: const Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(
              color: Colors.white,
              strokeWidth: 3,
            ),
            SizedBox(height: 16),
            Txt(
              txt: 'Obrada kupovine...',
              fontSize: 16,
              fontColor: Colors.white,
            ),
            SizedBox(height: 8),
            Txt(
              txt: 'Molimo sačekajte',
              fontSize: 14,
              fontColor: Colors.white70,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handlePurchase() async {
    if (selectedPackage == null) {
      getErrorSnackBar('Molimo vas da prvo odaberete paket');
      return;
    }

    if (!hasInternet) {
      getErrorSnackBar('Potrebna je internet konekcija za kupovinu');
      return;
    }

    setState(() => isPurchasing = true);

    final result = await PurchaseApi.purchasePackage(selectedPackage!);

    setState(() => isPurchasing = false);

    if (result.success) {
      // Success is handled in the payment controller
      // Navigation will happen automatically
    } else if (!result.cancelled && result.error != null) {
      // Error is already shown by payment controller
    }
  }

  Future<void> _handleRestorePurchases() async {
    if (!hasInternet) {
      getErrorSnackBar('Potrebna je internet konekcija');
      return;
    }

    setState(() => isPurchasing = true);
    final success = await PurchaseApi.restorePurchases();
    setState(() => isPurchasing = false);

    if (success && mounted) {
      // Refresh the UI
      setState(() {});
    }
  }

  Future<void> _handleManageSubscription() async {
    final url = await PurchaseApi.getManagementURL();
    if (url != null) {
      // Launch URL
      // You need to implement URL launcher
      // await launchUrl(Uri.parse(url));
      getSuccessSnackBar('Otvara se stranica za upravljanje pretplatom');
    } else {
      getErrorSnackBar('Nije moguće pristupiti upravljanju pretplatom');
    }
  }
}

// Keep the existing ColorfulSmokyEffect and SmokyGradientPainter classes as they are
class ColorfulSmokyEffect extends StatelessWidget {
  const ColorfulSmokyEffect({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        height: Get.height * 0.5,
        width: Get.width * 0.9,
        child: CustomPaint(
          painter: SmokyGradientPainter(),
        ),
      ),
    );
  }
}

class SmokyGradientPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Rect rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final Gradient gradient = LinearGradient(
      colors: [
        const Color(0xffED6A0B).withValues(alpha: 0.32),
        const Color(0xffAF52DE).withValues(alpha: 0.32),
      ],
      stops: const [0.1, 0.9],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
    final Paint paint = Paint()
      ..shader = gradient.createShader(rect)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 50);
    canvas.drawRect(rect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
