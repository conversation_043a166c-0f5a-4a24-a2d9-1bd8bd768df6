import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/models/question_model.dart';
import 'package:bibl/models/quiz_model.dart';
import 'package:bibl/services/db_service.dart';
import 'package:bibl/services/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:get/get.dart';

class QuizController extends GetxController {
  final DatabaseHelper dbHelper = DatabaseHelper();
  RxList<QuizModel> allQuizzes = <QuizModel>[].obs;
  RxList<ShuffleQuizModel> allShuffleQuizzes = <ShuffleQuizModel>[].obs;

  static const int quizChunkSize = 10;
  DocumentSnapshot? _lastQuizDoc;
  bool _hasMoreQuizzes = true;

  static const int shuffleChunkSize = 10;
  DocumentSnapshot? _lastShuffleQuizDoc;
  bool _hasMoreShuffleQuizzes = true;

  // Aggressive image preloading for super-fast display
  Future<void> startAggressiveImagePreloading() async {
    try {
      final lessonController = Get.find<LessonController>();

      // Load quizzes from local database first
      final localQuizzes = await dbHelper.getQuizzes();
      if (localQuizzes.isNotEmpty) {
        allQuizzes.assignAll(localQuizzes);
        await _preloadQuizImagesFromLocalData(lessonController);
      }

      // Load shuffle quizzes from local database
      final localShuffleQuizzes = await dbHelper.getShuffleQuizzes();
      if (localShuffleQuizzes.isNotEmpty) {
        allShuffleQuizzes.assignAll(localShuffleQuizzes);
        await _preloadShuffleQuizImagesFromLocalData(lessonController);
      }
    } catch (e) {
      // Error in quiz aggressive image preloading
    }
  }

  // Preload quiz images from locally cached data
  Future<void> _preloadQuizImagesFromLocalData(
      LessonController lessonController) async {
    final priorityQuizzes = allQuizzes.take(10).toList();

    for (var quiz in priorityQuizzes) {
      if (quiz.quizImageLink != null && quiz.quizImageLink!.isNotEmpty) {
        try {
          await lessonController.cacheManager
              .getSingleFile(quiz.quizImageLink!);
        } catch (e) {
          // Error preloading quiz image
        }
      }
    }

    // Continue with remaining quizzes in background
    _preloadRemainingQuizImagesInBackground(lessonController);
  }

  // Preload shuffle quiz images from locally cached data
  Future<void> _preloadShuffleQuizImagesFromLocalData(
      LessonController lessonController) async {
    final priorityShuffleQuizzes = allShuffleQuizzes.take(10).toList();

    for (var shuffleQuiz in priorityShuffleQuizzes) {
      for (var question in shuffleQuiz.questionsList ?? []) {
        if (question.qsImage != null && question.qsImage!.isNotEmpty) {
          try {
            await lessonController.cacheManager
                .getSingleFile(question.qsImage!);
          } catch (e) {
            // Error preloading shuffle quiz image
          }
        }
      }
    }

    // Continue with remaining shuffle quizzes in background
    _preloadRemainingShuffleQuizImagesInBackground(lessonController);
  }

  // Preload remaining quiz images in background
  void _preloadRemainingQuizImagesInBackground(
      LessonController lessonController) {
    Future.microtask(() async {
      final remainingQuizzes = allQuizzes.skip(10).toList();

      for (var quiz in remainingQuizzes) {
        if (quiz.quizImageLink != null && quiz.quizImageLink!.isNotEmpty) {
          try {
            await lessonController.cacheManager
                .getSingleFile(quiz.quizImageLink!);
          } catch (e) {
            // Error preloading background quiz image
          }
        }
      }
    });
  }

  // Preload remaining shuffle quiz images in background
  void _preloadRemainingShuffleQuizImagesInBackground(
      LessonController lessonController) {
    Future.microtask(() async {
      final remainingShuffleQuizzes = allShuffleQuizzes.skip(10).toList();

      for (var shuffleQuiz in remainingShuffleQuizzes) {
        for (var question in shuffleQuiz.questionsList ?? []) {
          if (question.qsImage != null && question.qsImage!.isNotEmpty) {
            try {
              await lessonController.cacheManager
                  .getSingleFile(question.qsImage!);
            } catch (e) {
              // Error preloading background shuffle quiz image
            }
          }
        }
      }
    });
  }

  Future<void> loadQuizzes() async {
    final quizzes = await dbHelper.getQuizzes();
    if (quizzes.isNotEmpty) {
      allQuizzes.assignAll(quizzes);
    }
    bool shouldUpdate = await shouldFetchFromFirestore();
    if (shouldUpdate) {
      await _fetchAllQuizzesByChunk();
    }
  }

  Future<void> loadShuffleQuizzes() async {
    final quizzes = await dbHelper.getShuffleQuizzes();
    if (quizzes.isNotEmpty) {
      allShuffleQuizzes.assignAll(quizzes);
    }
    bool shouldUpdate = await shouldFetchShuffleFromFirestore();
    if (shouldUpdate) {
      await _fetchAllShuffleQuizzesByChunk();
    }
  }

  Future<bool> shouldFetchFromFirestore() async {
    final lastSyncTime = SharedPrefs.getData(key: 'lastSyncTimeQuizzes') ?? 0;
    try {
      final latestFirestoreSnapshot = await FirebaseFirestore.instance
          .collection('quizes')
          .orderBy('lastUpdated', descending: true)
          .limit(1)
          .get();
      if (latestFirestoreSnapshot.docs.isNotEmpty) {
        final firestoreLatestTimestamp =
            (latestFirestoreSnapshot.docs.first['lastUpdated'] as Timestamp)
                .millisecondsSinceEpoch;
        return firestoreLatestTimestamp > lastSyncTime;
      }
      return true;
    } catch (e) {
      return true;
    }
  }

  Future<bool> shouldFetchShuffleFromFirestore() async {
    final lastSyncTime =
        SharedPrefs.getData(key: 'lastSyncTimeShuffleQuizzes') ?? 0;
    try {
      final latestFirestoreSnapshot = await FirebaseFirestore.instance
          .collection('shuffleQuizes')
          .orderBy('lastUpdated', descending: true)
          .limit(1)
          .get();
      if (latestFirestoreSnapshot.docs.isNotEmpty) {
        final firestoreLatestTimestamp =
            (latestFirestoreSnapshot.docs.first['lastUpdated'] as Timestamp)
                .millisecondsSinceEpoch;
        return firestoreLatestTimestamp > lastSyncTime;
      }
      return true;
    } catch (e) {
      return true;
    }
  }

  Future<void> _fetchAllQuizzesByChunk() async {
    final lastSyncTime = SharedPrefs.getData(key: 'lastSyncTimeQuizzes') ?? 0;
    _lastQuizDoc = null;
    _hasMoreQuizzes = true;

    while (_hasMoreQuizzes) {
      try {
        await _fetchQuizChunk(lastSyncTime);
      } catch (e) {
        _hasMoreQuizzes = false;
      }
    }

    await SharedPrefs.setData(
      key: 'lastSyncTimeQuizzes',
      value: DateTime.now().millisecondsSinceEpoch,
    );
  }

  Future<void> _fetchQuizChunk(int lastSyncTime) async {
    final LessonController lessonController = Get.find();
    try {
      Query query = FirebaseFirestore.instance
          .collection('quizes')
          .where('lastUpdated',
              isGreaterThan: Timestamp.fromMillisecondsSinceEpoch(lastSyncTime))
          .orderBy('lastUpdated', descending: false)
          .limit(quizChunkSize);

      if (_lastQuizDoc != null) {
        query = query.startAfterDocument(_lastQuizDoc!);
      }

      QuerySnapshot querySnapshot = await query.get();
      if (querySnapshot.docs.isEmpty) {
        _hasMoreQuizzes = false;
        return;
      }

      _lastQuizDoc = querySnapshot.docs.last;
      List<QuizModel> fetchedQuizzes = [];
      for (var quizDoc in querySnapshot.docs) {
        QuerySnapshot questionsSnapshot = await quizDoc.reference
            .collection('questionsList')
            .orderBy('qsNo')
            .get();

        List<QuestionModel> questions = questionsSnapshot.docs
            .map((qsDoc) => QuestionModel.fromSnap(qsDoc))
            .toList();

        QuizModel quiz = QuizModel.fromSnap(quizDoc)..questionsList = questions;
        if (quiz.quizImageLink != null && quiz.quizImageLink!.isNotEmpty) {
          await lessonController
              .fetchAndCacheImage(
            quiz.quizImageLink!,
            '${quiz.quizId}_image.jpg',
          )
              .catchError((e) {
            return null;
          });
        }
        fetchedQuizzes.add(quiz);
        await dbHelper.insertQuiz(quiz);
      }

      allQuizzes.addAll(fetchedQuizzes);
      lessonController.mergeAndShuffleItems(
          fetchedQuizzes: fetchedQuizzes,
          isShuffle: false,
          from: 'quiz chucks',
          shouldClear: false);
      lessonController.shuffleAllItems(
          fetchedQuizzes: fetchedQuizzes,
          shouldClear: false,
          isShuffle: false,
          from: 'quiz shuffle all chucks');

      if (querySnapshot.docs.length < quizChunkSize) {
        _hasMoreQuizzes = false;
      }
    } catch (e) {
      _hasMoreQuizzes = false;
    }
  }

  Future<void> _fetchAllShuffleQuizzesByChunk() async {
    final lastSyncTime =
        SharedPrefs.getData(key: 'lastSyncTimeShuffleQuizzes') ?? 0;
    _lastShuffleQuizDoc = null;
    _hasMoreShuffleQuizzes = true;

    while (_hasMoreShuffleQuizzes) {
      try {
        await _fetchShuffleQuizChunk(lastSyncTime);
      } catch (e) {
        _hasMoreShuffleQuizzes = false;
      }
    }

    await SharedPrefs.setData(
      key: 'lastSyncTimeShuffleQuizzes',
      value: DateTime.now().millisecondsSinceEpoch,
    );
  }

  Future<void> _fetchShuffleQuizChunk(int lastSyncTime) async {
    final LessonController lessonController = Get.find();
    try {
      Query query = FirebaseFirestore.instance
          .collection('shuffleQuizes')
          .where('lastUpdated',
              isGreaterThan: Timestamp.fromMillisecondsSinceEpoch(lastSyncTime))
          .orderBy('lastUpdated', descending: false)
          .limit(shuffleChunkSize);

      if (_lastShuffleQuizDoc != null) {
        query = query.startAfterDocument(_lastShuffleQuizDoc!);
      }

      QuerySnapshot querySnapshot = await query.get();
      if (querySnapshot.docs.isEmpty) {
        _hasMoreShuffleQuizzes = false;
        return;
      }

      _lastShuffleQuizDoc = querySnapshot.docs.last;
      List<ShuffleQuizModel> fetchedShuffleQuizzes = [];
      for (var quizDoc in querySnapshot.docs) {
        QuerySnapshot questionsSnapshot = await quizDoc.reference
            .collection('questionsList')
            .orderBy('qsNo')
            .get();

        List<ShuffleQuizQuestionModel> questions = questionsSnapshot.docs
            .map((qsDoc) => ShuffleQuizQuestionModel.fromSnap(qsDoc))
            .toList();

        ShuffleQuizModel shuffleQuiz = ShuffleQuizModel.fromSnap(quizDoc)
          ..questionsList = questions;
        if (shuffleQuiz.questionsList != null) {
          for (var qs in shuffleQuiz.questionsList!) {
            if (qs.qsImage != null && qs.qsImage!.isNotEmpty) {
              await lessonController
                  .fetchAndCacheImage(
                qs.qsImage!,
                '${shuffleQuiz.quizId}_${qs.qsNo}.jpg',
              )
                  .catchError((e) {
                return null;
              });
            }
          }
        }
        fetchedShuffleQuizzes.add(shuffleQuiz);
        await dbHelper.insertShuffleQuiz(shuffleQuiz);
      }

      allShuffleQuizzes.addAll(fetchedShuffleQuizzes);
      lessonController.mergeAndShuffleItems(
          fetchedShuffleQuizzes: fetchedShuffleQuizzes,
          isShuffle: false,
          from: 'shuffle quiz chucks',
          shouldClear: false);
      lessonController.shuffleAllItems(
          fetchedShuffleQuizzes: fetchedShuffleQuizzes,
          shouldClear: false,
          isShuffle: false,
          from: 'shuffle quiz shuffle all chucks');

      if (querySnapshot.docs.length < shuffleChunkSize) {
        _hasMoreShuffleQuizzes = false;
      }
    } catch (e) {
      _hasMoreShuffleQuizzes = false;
    }
  }
}
