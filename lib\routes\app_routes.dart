import 'package:bibl/bnb.dart';
import 'package:bibl/splash.dart';
import 'package:bibl/views/auth_screens/auth.dart';
import 'package:bibl/views/auth_screens/email_register.dart';
import 'package:bibl/views/auth_screens/forgot_password.dart';
import 'package:bibl/views/auth_screens/login.dart';
import 'package:bibl/views/onboarding/onboarding1.dart';
import 'package:bibl/views/onboarding/onboarding2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Professional route management system for the app
///
/// This class defines all routes in a centralized location and provides
/// consistent transition animations and durations for a professional feel.
class AppRoutes {
  // Route names
  static const String splash = '/splash';
  static const String auth = '/auth';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String home = '/home';
  static const String onboarding1 = '/onboarding1';
  static const String onboarding2 = '/onboarding2';

  // Default transition settings
  static const Transition defaultTransition = Transition.fadeIn;
  static const Duration defaultDuration = Duration(milliseconds: 300);

  // Get all routes
  static List<GetPage> getPages() {
    return [
      GetPage(
        name: splash,
        page: () => const Splash(),
        transition: Transition.fadeIn,
        transitionDuration: const Duration(milliseconds: 400),
      ),
      GetPage(
        name: auth,
        page: () => const Authentication(),
        transition: Transition.fadeIn,
        transitionDuration: const Duration(milliseconds: 400),
      ),
      GetPage(
        name: login,
        page: () => const Login(),
        transition: Transition.rightToLeft,
        transitionDuration: const Duration(milliseconds: 300),
      ),
      GetPage(
        name: register,
        page: () => const EmailRegister(),
        transition: Transition.rightToLeft,
        transitionDuration: const Duration(milliseconds: 300),
      ),
      GetPage(
        name: forgotPassword,
        page: () => const ForgotPassword(),
        transition: Transition.rightToLeft,
        transitionDuration: const Duration(milliseconds: 300),
      ),
      GetPage(
        name: home,
        page: () => const BNB(),
        transition: Transition.fadeIn,
        transitionDuration: const Duration(milliseconds: 400),
      ),
      GetPage(
        name: onboarding1,
        page: () => const OnBoarding1(),
        transition: Transition.fadeIn,
        transitionDuration: const Duration(milliseconds: 400),
      ),
      GetPage(
        name: onboarding2,
        page: () => const Onboarding2(),
        transition: Transition.rightToLeft,
        transitionDuration: const Duration(milliseconds: 300),
      ),
    ];
  }

  // Navigate to a named route with consistent animation
  static Future<T?> navigateTo<T>(
    String routeName, {
    dynamic arguments,
    int? id,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
  }) {
    return Get.toNamed(
          routeName,
          arguments: arguments,
          id: id,
          preventDuplicates: preventDuplicates,
          parameters: parameters,
        ) ??
        Future.value(null);
  }

  // Replace current screen with named route
  static Future<T?> navigateOff<T>(
    String routeName, {
    dynamic arguments,
    int? id,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
  }) {
    return Get.offNamed(
          routeName,
          arguments: arguments,
          id: id,
          preventDuplicates: preventDuplicates,
          parameters: parameters,
        ) ??
        Future.value(null);
  }

  // Replace all screens with named route
  static Future<T?> navigateOffAll<T>(
    String routeName, {
    dynamic arguments,
    int? id,
    Map<String, String>? parameters,
  }) {
    return Get.offAllNamed(
          routeName,
          arguments: arguments,
          id: id,
          parameters: parameters,
        ) ??
        Future.value(null);
  }

  // Navigate back
  static void navigateBack<T>({T? result}) {
    if (Get.currentRoute != splash && Navigator.canPop(Get.context!)) {
      Get.back(result: result);
    }
  }
}
