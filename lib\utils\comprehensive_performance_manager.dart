import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import '../widgets/ultra_fast_image.dart';
import 'enhanced_cache_manager.dart';

/// Comprehensive performance manager for ultra-fast app experience
class ComprehensivePerformanceManager {
  static final ComprehensivePerformanceManager _instance = 
      ComprehensivePerformanceManager._internal();
  
  factory ComprehensivePerformanceManager() => _instance;
  ComprehensivePerformanceManager._internal();

  Timer? _performanceMonitorTimer;
  Timer? _memoryCleanupTimer;
  Timer? _cacheOptimizationTimer;
  
  int _frameDropCounter = 0;
  double _averageFPS = 60.0;
  bool _isPerformanceMonitoringEnabled = false;

  /// Initialize comprehensive performance monitoring
  void initializePerformanceMonitoring() {
    if (_isPerformanceMonitoringEnabled) return;
    
    _isPerformanceMonitoringEnabled = true;
    
    // Start frame rate monitoring
    _startFrameRateMonitoring();
    
    // Start memory cleanup cycles
    _startMemoryManagement();
    
    // Start cache optimization
    _startCacheOptimization();
    
    // Optimize system UI for performance
    _optimizeSystemUI();
    
    debugPrint('🚀 Comprehensive Performance Manager initialized');
  }

  /// Start monitoring frame rate and performance
  void _startFrameRateMonitoring() {
    if (!kDebugMode) return;
    
    int frameCount = 0;
    Duration lastTimestamp = Duration.zero;
    Duration totalElapsed = Duration.zero;

    SchedulerBinding.instance.addPersistentFrameCallback((timeStamp) {
      if (lastTimestamp == Duration.zero) {
        lastTimestamp = timeStamp;
        return;
      }

      frameCount++;
      final frameDuration = timeStamp - lastTimestamp;
      totalElapsed += frameDuration;
      lastTimestamp = timeStamp;

      // Check for frame drops (>16.67ms for 60fps)
      if (frameDuration.inMilliseconds > 16) {
        _frameDropCounter++;
      }

      // Calculate average FPS every 60 frames
      if (frameCount >= 60) {
        final fps = (frameCount * 1000000) / totalElapsed.inMicroseconds;
        _averageFPS = fps;
        
        if (fps < 45) {
          debugPrint('⚠️ Performance Warning: FPS dropped to ${fps.toStringAsFixed(1)}');
          _triggerPerformanceOptimization();
        }
        
        frameCount = 0;
        totalElapsed = Duration.zero;
        _frameDropCounter = 0;
      }
    });
  }

  /// Start automatic memory management
  void _startMemoryManagement() {
    _memoryCleanupTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _performMemoryCleanup(),
    );
  }

  /// Start cache optimization cycles
  void _startCacheOptimization() {
    _cacheOptimizationTimer = Timer.periodic(
      const Duration(minutes: 10),
      (_) => _optimizeCaches(),
    );
  }

  /// Optimize system UI settings
  void _optimizeSystemUI() {
    // Enable edge-to-edge mode for better performance
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    
    // Set preferred orientations for consistent layout
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    
    // Optimize status bar
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Color(0x00000000), // Transparent
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Color(0x00000000), // Transparent
      ),
    );
  }

  /// Perform comprehensive memory cleanup
  Future<void> _performMemoryCleanup() async {
    try {
      // Clear expired image preload entries
      UltraFastImagePreloader.clearExpiredEntries();
      
      // Clean up image cache if memory usage is high
      if (!CacheMemoryManager.canLoadMore()) {
        await EnhancedCacheManager.clearCache(onlyExpired: true);
        debugPrint('🧹 Memory cleanup: Cleared expired caches');
      }
      
      // Trigger garbage collection
      if (Platform.isAndroid || Platform.isIOS) {
        // Force garbage collection on mobile platforms
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
    } catch (e) {
      debugPrint('Error during memory cleanup: $e');
    }
  }

  /// Optimize caches for better performance
  Future<void> _optimizeCaches() async {
    try {
      // Get cache size and optimize if needed
      final cacheSize = await EnhancedCacheManager.getCacheSize();
      final cacheSizeMB = cacheSize / (1024 * 1024);
      
      if (cacheSizeMB > 150) { // If cache is larger than 150MB
        await EnhancedCacheManager.clearCache(onlyExpired: true);
        debugPrint('🗂️ Cache optimization: Cleared ${cacheSizeMB.toStringAsFixed(1)}MB');
      }
      
    } catch (e) {
      debugPrint('Error during cache optimization: $e');
    }
  }

  /// Trigger performance optimization when FPS drops
  void _triggerPerformanceOptimization() {
    // Reduce image loading concurrency
    debugPrint('⚡ Triggering performance optimization');
    
    // Clear some caches to free up memory
    CacheMemoryManager.clearMemoryTracking();
    
    // Reduce concurrent operations
    _reduceOperationComplexity();
  }

  /// Reduce operation complexity for better performance
  void _reduceOperationComplexity() {
    try {
      // Reduce image preloading and complexity
      debugPrint('🔧 Reducing operation complexity for better performance');
      
      // Clear some memory to improve performance
      UltraFastImagePreloader.clearExpiredEntries();
      
    } catch (e) {
      // Controllers might not be initialized
      debugPrint('Error reducing complexity: $e');
    }
  }

  /// Optimize widget rebuilds using debouncing
  static Timer? _debounceTimer;
  static void debounceOperation(VoidCallback operation, {
    Duration delay = const Duration(milliseconds: 300),
  }) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, operation);
  }

  /// Run expensive operations without blocking UI
  static Future<T> runExpensiveOperation<T>(
    Future<T> Function() operation, {
    String? operationName,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      T result;
      
      if (kIsWeb) {
        result = await operation();
      } else {
        // Use compute for CPU-intensive operations
        result = await compute((_) => operation(), null);
      }
      
      stopwatch.stop();
      if (operationName != null && kDebugMode) {
        debugPrint('✅ $operationName completed in ${stopwatch.elapsedMilliseconds}ms');
      }
      
      return result;
    } catch (e) {
      stopwatch.stop();
      if (operationName != null) {
        debugPrint('❌ $operationName failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      }
      rethrow;
    }
  }

  /// Defer heavy operations to next frame
  static Future<void> deferToNextFrame(VoidCallback callback) {
    return SchedulerBinding.instance.endOfFrame.then((_) {
      if (SchedulerBinding.instance.schedulerPhase == SchedulerPhase.idle) {
        callback();
      } else {
        SchedulerBinding.instance.addPostFrameCallback((_) => callback());
      }
    });
  }

  /// Batch multiple operations for better performance
  static Future<List<T>> batchOperations<T>(
    List<Future<T> Function()> operations, {
    int concurrency = 3,
    String? batchName,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final results = <T>[];
      final chunks = _chunkList(operations, concurrency);
      
      for (final chunk in chunks) {
        final chunkResults = await Future.wait(
          chunk.map((op) => op()),
          eagerError: false,
        );
        results.addAll(chunkResults);
        
        // Small delay between chunks
        if (chunks.length > 1) {
          await Future.delayed(const Duration(milliseconds: 10));
        }
      }
      
      stopwatch.stop();
      if (batchName != null && kDebugMode) {
        debugPrint('📦 $batchName batch completed in ${stopwatch.elapsedMilliseconds}ms');
      }
      
      return results;
    } catch (e) {
      stopwatch.stop();
      if (batchName != null) {
        debugPrint('❌ $batchName batch failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      }
      rethrow;
    }
  }

  /// Chunk list into smaller pieces
  static List<List<T>> _chunkList<T>(List<T> list, int chunkSize) {
    final chunks = <List<T>>[];
    for (var i = 0; i < list.length; i += chunkSize) {
      final end = (i + chunkSize < list.length) ? i + chunkSize : list.length;
      chunks.add(list.sublist(i, end));
    }
    return chunks;
  }

  /// Get current performance statistics
  Map<String, dynamic> getPerformanceStats() {
    return {
      'averageFPS': _averageFPS.toStringAsFixed(1),
      'frameDrops': _frameDropCounter,
      'memoryUsage': CacheMemoryManager.getMemoryUsageString(),
      'preloadStats': UltraFastImagePreloader.getPreloadStats(),
      'isOptimized': _averageFPS > 50,
    };
  }

  /// Force comprehensive cleanup (called on logout)
  Future<void> forceComprehensiveCleanup() async {
    try {
      // Clear all caches
      await EnhancedCacheManager.forceClearAllCaches();
      
      // Clear preload tracking
      UltraFastImagePreloader.clearPreloadTracking();
      
      // Reset memory tracking
      CacheMemoryManager.clearMemoryTracking();
      
      // Cancel timers
      _debounceTimer?.cancel();
      
      debugPrint('🔄 Force comprehensive cleanup completed');
      
    } catch (e) {
      debugPrint('Error during force cleanup: $e');
    }
  }

  /// Dispose and cleanup
  void dispose() {
    _performanceMonitorTimer?.cancel();
    _memoryCleanupTimer?.cancel();
    _cacheOptimizationTimer?.cancel();
    _debounceTimer?.cancel();
    _isPerformanceMonitoringEnabled = false;
  }
}

/// Mixin for widgets to automatically optimize performance
mixin PerformanceOptimizedWidget {
  /// Use this instead of setState for debounced updates
  void setStateOptimized(VoidCallback fn) {
    ComprehensivePerformanceManager.debounceOperation(() {
      if (mounted) fn();
    });
  }
  
  /// Check if the widget is still mounted before expensive operations
  bool get mounted;
}