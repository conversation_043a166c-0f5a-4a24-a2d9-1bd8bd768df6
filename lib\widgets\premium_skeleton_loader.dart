import 'package:flutter/material.dart';

/// Premium skeleton loader with smooth animations and branded styling
class PremiumSkeletonLoader extends StatefulWidget {
  final double? width;
  final double? height;
  final double borderRadius;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final int itemCount;
  final Widget Function(BuildContext, int)? itemBuilder;

  const PremiumSkeletonLoader({
    Key? key,
    this.width,
    this.height,
    this.borderRadius = 16.0,
    this.margin,
    this.padding,
    this.itemCount = 1,
    this.itemBuilder,
  }) : super(key: key);

  /// Premium card skeleton
  const PremiumSkeletonLoader.card({
    Key? key,
    this.margin = const EdgeInsets.all(16),
    this.padding = const EdgeInsets.all(16),
  }) : width = null,
       height = 200,
       borderRadius = 16.0,
       itemCount = 1,
       itemBuilder = null,
       super(key: key);

  /// Premium list skeleton
  const PremiumSkeletonLoader.list({
    Key? key,
    this.itemCount = 5,
    this.margin = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
  }) : width = null,
       height = 80,
       borderRadius = 12.0,
       padding = null,
       itemBuilder = null,
       super(key: key);

  /// Premium profile skeleton
  const PremiumSkeletonLoader.profile({
    Key? key,
    this.margin = const EdgeInsets.all(20),
  }) : width = null,
       height = null,
       borderRadius = 16.0,
       padding = null,
       itemCount = 1,
       itemBuilder = null,
       super(key: key);

  @override
  State<PremiumSkeletonLoader> createState() => _PremiumSkeletonLoaderState();
}

class _PremiumSkeletonLoaderState extends State<PremiumSkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1800),
      vsync: this,
    )..repeat();
    
    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.itemBuilder != null) {
      return Column(
        children: List.generate(
          widget.itemCount,
          (index) => widget.itemBuilder!(context, index),
        ),
      );
    }

    if (widget.itemCount > 1) {
      return Column(
        children: List.generate(
          widget.itemCount,
          (index) => _buildSingleSkeleton(),
        ),
      );
    }

    return _buildSingleSkeleton();
  }

  Widget _buildSingleSkeleton() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          margin: widget.margin,
          padding: widget.padding,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            gradient: LinearGradient(
              begin: Alignment(_shimmerAnimation.value - 1, 0),
              end: Alignment(_shimmerAnimation.value, 0),
              colors: _getShimmerColors(),
              stops: const [0.0, 0.5, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: widget.height == null ? _buildProfileSkeletonContent() : null,
        );
      },
    );
  }

  List<Color> _getShimmerColors() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    if (isDark) {
      return [
        const Color(0xFF2A2A2A),
        const Color(0xFF3A3A3A),
        const Color(0xFF2A2A2A),
      ];
    } else {
      return [
        const Color(0xFFE8E8E8),
        const Color(0xFFF5F5F5),
        const Color(0xFFE8E8E8),
      ];
    }
  }

  Widget _buildProfileSkeletonContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Profile header
        Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(30),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: 120,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
        // Stats row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(3, (index) {
            return Column(
              children: [
                Container(
                  width: 40,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  width: 60,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ],
            );
          }),
        ),
      ],
    );
  }
}

/// Premium loading overlay for full-screen loading states
class PremiumLoadingOverlay extends StatefulWidget {
  final Widget child;
  final bool isLoading;
  final String? loadingText;
  final Color? overlayColor;

  const PremiumLoadingOverlay({
    Key? key,
    required this.child,
    required this.isLoading,
    this.loadingText,
    this.overlayColor,
  }) : super(key: key);

  @override
  State<PremiumLoadingOverlay> createState() => _PremiumLoadingOverlayState();
}

class _PremiumLoadingOverlayState extends State<PremiumLoadingOverlay>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void didUpdateWidget(PremiumLoadingOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _fadeController.forward();
      } else {
        _fadeController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (widget.isLoading)
          FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              color: widget.overlayColor ?? Colors.black.withValues(alpha: 0.7),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _pulseAnimation.value,
                          child: Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(30),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  spreadRadius: 10,
                                ),
                              ],
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(
                                strokeWidth: 3,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Color(0xFF6C5CE7),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    if (widget.loadingText != null) ...[
                      const SizedBox(height: 24),
                      Text(
                        widget.loadingText!,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}