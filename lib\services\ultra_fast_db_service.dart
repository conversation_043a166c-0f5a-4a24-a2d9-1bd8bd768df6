import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as p;
import '../models/lesson_model.dart';
import '../models/page_model.dart';

/// Ultra-fast database service with connection pooling and background operations
class UltraFastDatabaseService {
  static UltraFastDatabaseService? _instance;
  static Database? _database;
  static final Map<String, Completer<List<dynamic>>> _queryCache = {};
  static Timer? _cacheCleanupTimer;

  factory UltraFastDatabaseService() {
    _instance ??= UltraFastDatabaseService._internal();
    return _instance!;
  }

  UltraFastDatabaseService._internal() {
    _startCacheCleanup();
  }

  /// Get optimized database instance with connection pooling
  Future<Database> get database async {
    if (_database != null) return _database!;

    _database = await _initializeOptimizedDatabase();
    return _database!;
  }

  /// Initialize database with performance optimizations
  Future<Database> _initializeOptimizedDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = p.join(dbPath, 'ultra_fast_app.db');

    return await openDatabase(
      path,
      version: 2, // Incremented for optimizations
      onCreate: _createOptimizedTables,
      onUpgrade: _upgradeDatabase,
      onOpen: _optimizeDatabase,
    );
  }

  /// Create optimized database tables with proper indexing
  Future<void> _createOptimizedTables(Database db, int version) async {
    await db.transaction((txn) async {
      // Optimized lessons table with indexes
      await txn.execute('''
        CREATE TABLE lessons (
          lessonId TEXT PRIMARY KEY NOT NULL,
          lessonName TEXT,
          lessonNo INTEGER,
          intro TEXT,
          audioLink TEXT,
          imageLink TEXT,
          category TEXT,
          lastUpdated INTEGER,
          isPreloaded INTEGER DEFAULT 0
        );
      ''');

      // Create indexes for faster queries
      await txn
          .execute('CREATE INDEX idx_lessons_category ON lessons(category);');
      await txn
          .execute('CREATE INDEX idx_lessons_lessonNo ON lessons(lessonNo);');
      await txn.execute(
          'CREATE INDEX idx_lessons_lastUpdated ON lessons(lastUpdated);');

      // Optimized pages table
      await txn.execute('''
        CREATE TABLE pages (
          lessonId TEXT NOT NULL,
          pageNo INTEGER NOT NULL,
          pageTitle TEXT,
          pagePhotoLink TEXT,
          pageContent TEXT,
          isPreloaded INTEGER DEFAULT 0,
          PRIMARY KEY(lessonId, pageNo),
          FOREIGN KEY(lessonId) REFERENCES lessons(lessonId) ON DELETE CASCADE
        );
      ''');

      await txn.execute('CREATE INDEX idx_pages_lessonId ON pages(lessonId);');

      // Optimized quizzes table
      await txn.execute('''
        CREATE TABLE quizzes (
          quizId TEXT PRIMARY KEY NOT NULL,
          quizName TEXT,
          quizNo INTEGER,
          quizImageLink TEXT,
          category TEXT,
          lastUpdated INTEGER,
          isPreloaded INTEGER DEFAULT 0
        );
      ''');

      await txn
          .execute('CREATE INDEX idx_quizzes_category ON quizzes(category);');
      await txn.execute('CREATE INDEX idx_quizzes_quizNo ON quizzes(quizNo);');

      // Other tables...
      await _createRemainingTables(txn);
    });
  }

  Future<void> _createRemainingTables(Transaction txn) async {
    // Quiz questions table
    await txn.execute('''
      CREATE TABLE quizquestions (
        quizId TEXT NOT NULL,
        qsNo INTEGER NOT NULL,
        question TEXT,
        correctOption TEXT,
        options TEXT,
        PRIMARY KEY(quizId, qsNo),
        FOREIGN KEY(quizId) REFERENCES quizzes(quizId) ON DELETE CASCADE
      );
    ''');

    // Shuffle quizzes table
    await txn.execute('''
      CREATE TABLE shufflequizzes (
        quizId TEXT PRIMARY KEY NOT NULL,
        quizName TEXT,
        category TEXT,
        lastUpdated INTEGER,
        isPreloaded INTEGER DEFAULT 0
      );
    ''');

    // Shuffle quiz questions table
    await txn.execute('''
      CREATE TABLE shufflequizquestions (
        quizId TEXT NOT NULL,
        qsNo INTEGER NOT NULL,
        question TEXT,
        qsImage TEXT,
        correctOption TEXT,
        options TEXT,
        PRIMARY KEY(quizId, qsNo),
        FOREIGN KEY(quizId) REFERENCES shufflequizzes(quizId) ON DELETE CASCADE
      );
    ''');
  }

  /// Upgrade database with new optimizations
  Future<void> _upgradeDatabase(
      Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Add new columns for optimization
      await db.execute(
          'ALTER TABLE lessons ADD COLUMN isPreloaded INTEGER DEFAULT 0;');
      await db.execute(
          'ALTER TABLE pages ADD COLUMN isPreloaded INTEGER DEFAULT 0;');
      await db.execute(
          'ALTER TABLE quizzes ADD COLUMN isPreloaded INTEGER DEFAULT 0;');
      await db.execute(
          'ALTER TABLE shufflequizzes ADD COLUMN isPreloaded INTEGER DEFAULT 0;');

      // Add new indexes
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_lessons_category ON lessons(category);');
      await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_lessons_lessonNo ON lessons(lessonNo);');
    }
  }

  /// Optimize database settings for performance
  Future<void> _optimizeDatabase(Database db) async {
    await db.execute('PRAGMA journal_mode=WAL;'); // Write-Ahead Logging
    await db.execute('PRAGMA synchronous=NORMAL;'); // Faster writes
    await db.execute('PRAGMA cache_size=10000;'); // Larger cache
    await db.execute('PRAGMA temp_store=MEMORY;'); // Use memory for temp
    await db.execute('PRAGMA mmap_size=268435456;'); // 256MB memory mapping
  }

  /// Ultra-fast lesson retrieval with caching
  Future<List<LessonModel>> getLessonsUltraFast({String? category}) async {
    final cacheKey = 'lessons_${category ?? 'all'}';

    // Check cache first
    if (_queryCache.containsKey(cacheKey)) {
      final result = await _queryCache[cacheKey]!.future;
      return result.cast<LessonModel>();
    }

    // Create completer for this query
    final completer = Completer<List<dynamic>>();
    _queryCache[cacheKey] = completer;

    try {
      final db = await database;

      String query = '''
        SELECT 
          l.lessonId, l.lessonName, l.lessonNo, l.intro, l.audioLink, l.imageLink, l.category,
          p.pageNo, p.pageTitle, p.pagePhotoLink, p.pageContent
        FROM lessons AS l
        LEFT JOIN pages AS p ON l.lessonId = p.lessonId
      ''';

      List<Object?> args = [];
      if (category != null) {
        query += ' WHERE l.category = ?';
        args.add(category);
      }

      query += ' ORDER BY l.lessonNo, p.pageNo;';

      final rawData = await db.rawQuery(query, args);
      final lessons = _processLessonData(rawData);

      completer.complete(lessons);
      return lessons;
    } catch (e) {
      completer.completeError(e);
      rethrow;
    } finally {
      // Remove from cache after a delay
      Timer(const Duration(minutes: 5), () {
        _queryCache.remove(cacheKey);
      });
    }
  }

  /// Process lesson data efficiently
  List<LessonModel> _processLessonData(List<Map<String, dynamic>> rawData) {
    final Map<String, LessonModel> lessonMap = {};

    for (final row in rawData) {
      final lessonId = row['lessonId'] as String;

      // Create lesson if not exists
      if (!lessonMap.containsKey(lessonId)) {
        lessonMap[lessonId] = LessonModel(
          lessonId: lessonId,
          lessonName: row['lessonName'] as String?,
          lessonNo: row['lessonNo'] as int?,
          intro: row['intro'] as String?,
          audioLink: row['audioLink'] as String?,
          imageLink: row['imageLink'] as String?,
          category: row['category'] as String?,
          pages: [],
        );
      }

      // Add page if exists
      if (row['pageNo'] != null) {
        final page = PageModel(
          pageNo: row['pageNo'] as int?,
          pageTitle: row['pageTitle'] as String?,
          pagePhotoLink: row['pagePhotoLink'] as String?,
          pageContent: row['pageContent'] as String?,
        );
        lessonMap[lessonId]!.pages!.add(page);
      }
    }

    return lessonMap.values.toList();
  }

  /// Batch insert with transaction for maximum performance
  Future<void> batchInsertLessons(List<LessonModel> lessons) async {
    final db = await database;

    await db.transaction((txn) async {
      final batch = txn.batch();

      for (final lesson in lessons) {
        // Insert lesson
        batch.insert(
          'lessons',
          lesson.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );

        // Insert pages
        if (lesson.pages != null) {
          for (final page in lesson.pages!) {
            batch.insert(
              'pages',
              page.toMap(lesson.lessonId!),
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }
        }
      }

      await batch.commit(noResult: true);
    });

    // Clear relevant caches
    _clearLessonCaches();
  }

  /// Clear lesson-related caches
  void _clearLessonCaches() {
    final keysToRemove =
        _queryCache.keys.where((key) => key.startsWith('lessons_')).toList();

    for (final key in keysToRemove) {
      _queryCache.remove(key);
    }
  }

  /// Start periodic cache cleanup
  void _startCacheCleanup() {
    _cacheCleanupTimer = Timer.periodic(
      const Duration(minutes: 10),
      (_) => _cleanupCache(),
    );
  }

  /// Clean up old cache entries
  void _cleanupCache() {
    if (_queryCache.length > 50) {
      debugPrint('🧹 Cleaning up database cache...');
      _queryCache.clear();
    }
  }

  /// Dispose resources
  void dispose() {
    _cacheCleanupTimer?.cancel();
    _queryCache.clear();
    _database?.close();
    _database = null;
  }
}
