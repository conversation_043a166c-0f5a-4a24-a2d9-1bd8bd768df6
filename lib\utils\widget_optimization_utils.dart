import 'dart:async';
import 'package:flutter/material.dart';

/// Advanced widget optimization utilities for premium performance
class WidgetOptimizationUtils {
  // Private constructor to prevent instantiation
  WidgetOptimizationUtils._();

  /// Wraps a widget with optimized RepaintBoundary for performance
  static Widget optimizedRepaintBoundary({
    required Widget child,
    String? debugLabel,
    bool forceRepaint = false,
  }) {
    return RepaintBoundary(
      key: debugLabel != null ? ValueKey('repaint_$debugLabel') : null,
      child: child,
    );
  }

  /// Creates an optimized ListView with better performance characteristics
  static Widget optimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    EdgeInsetsGeometry? padding,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    double? cacheExtent,
    int? semanticChildCount,
  }) {
    return ListView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics ?? const BouncingScrollPhysics(),
      itemCount: itemCount,
      // Optimize cache extent for memory efficiency
      cacheExtent: cacheExtent ?? 500.0,
      semanticChildCount: semanticChildCount,
      itemBuilder: (context, index) {
        return optimizedRepaintBoundary(
          debugLabel: 'list_item_$index',
          child: itemBuilder(context, index),
        );
      },
    );
  }

  /// Creates a memory-efficient grid view
  static Widget optimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required SliverGridDelegate gridDelegate,
    ScrollController? controller,
    EdgeInsetsGeometry? padding,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    double? cacheExtent,
  }) {
    return GridView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics ?? const BouncingScrollPhysics(),
      gridDelegate: gridDelegate,
      itemCount: itemCount,
      cacheExtent: cacheExtent ?? 400.0,
      itemBuilder: (context, index) {
        return optimizedRepaintBoundary(
          debugLabel: 'grid_item_$index',
          child: itemBuilder(context, index),
        );
      },
    );
  }

  /// Creates an optimized CustomScrollView with performance benefits
  static Widget optimizedCustomScrollView({
    required List<Widget> slivers,
    ScrollController? controller,
    ScrollPhysics? physics,
    double? cacheExtent,
    Key? key,
  }) {
    return CustomScrollView(
      key: key,
      controller: controller,
      physics: physics ?? const BouncingScrollPhysics(),
      cacheExtent: cacheExtent ?? 600.0,
      slivers: slivers.map((sliver) {
        return optimizedRepaintBoundary(
          child: sliver,
        );
      }).toList(),
    );
  }

  /// Optimized image widget with better memory management
  static Widget optimizedImage({
    required ImageProvider imageProvider,
    BoxFit? fit,
    double? width,
    double? height,
    Color? color,
    BlendMode? colorBlendMode,
    AlignmentGeometry alignment = Alignment.center,
    String? semanticLabel,
    bool enableMemoryCache = true,
  }) {
    return optimizedRepaintBoundary(
      child: Image(
        image: imageProvider,
        fit: fit,
        width: width,
        height: height,
        color: color,
        colorBlendMode: colorBlendMode,
        alignment: alignment,
        semanticLabel: semanticLabel,
        // Optimize for memory usage
        gaplessPlayback: true,
        filterQuality: FilterQuality.medium,
        isAntiAlias: true,
        // Enable memory cache for better performance
        frameBuilder: enableMemoryCache
            ? (context, child, frame, wasSynchronouslyLoaded) {
                if (wasSynchronouslyLoaded) return child;
                return AnimatedOpacity(
                  opacity: frame == null ? 0 : 1,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                  child: child,
                );
              }
            : null,
      ),
    );
  }

  /// Creates a performance-optimized container with reduced rebuilds
  static Widget optimizedContainer({
    Key? key,
    AlignmentGeometry? alignment,
    EdgeInsetsGeometry? padding,
    Color? color,
    Decoration? decoration,
    Decoration? foregroundDecoration,
    double? width,
    double? height,
    BoxConstraints? constraints,
    EdgeInsetsGeometry? margin,
    Matrix4? transform,
    AlignmentGeometry? transformAlignment,
    required Widget child,
    Clip clipBehavior = Clip.none,
  }) {
    return optimizedRepaintBoundary(
      child: Container(
        key: key,
        alignment: alignment,
        padding: padding,
        color: color,
        decoration: decoration,
        foregroundDecoration: foregroundDecoration,
        width: width,
        height: height,
        constraints: constraints,
        margin: margin,
        transform: transform,
        transformAlignment: transformAlignment,
        clipBehavior: clipBehavior,
        child: child,
      ),
    );
  }

  /// Optimized text widget with better performance
  static Widget optimizedText(
    String text, {
    Key? key,
    TextStyle? style,
    StrutStyle? strutStyle,
    TextAlign? textAlign,
    TextDirection? textDirection,
    Locale? locale,
    bool? softWrap,
    TextOverflow? overflow,
    double? textScaleFactor,
    int? maxLines,
    String? semanticsLabel,
    TextWidthBasis? textWidthBasis,
    TextHeightBehavior? textHeightBehavior,
    bool enableOptimization = true,
  }) {
    final textWidget = Text(
      text,
      key: key,
      style: style,
      strutStyle: strutStyle,
      textAlign: textAlign,
      textDirection: textDirection,
      locale: locale,
      softWrap: softWrap,
      overflow: overflow,
      maxLines: maxLines,
      semanticsLabel: semanticsLabel,
      textWidthBasis: textWidthBasis,
      textHeightBehavior: textHeightBehavior,
    );

    return enableOptimization
        ? optimizedRepaintBoundary(child: textWidget)
        : textWidget;
  }

  /// Creates a lazy loading wrapper for expensive widgets
  static Widget lazyBuilder({
    required Widget Function() builder,
    Widget? placeholder,
    Duration delay = const Duration(milliseconds: 100),
  }) {
    return FutureBuilder<Widget>(
      future: Future.delayed(delay, builder),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return snapshot.data!;
        }
        return placeholder ?? const SizedBox.shrink();
      },
    );
  }

  /// Advanced viewport-based widget management
  static Widget viewportAwareBuilder({
    required Widget Function(BuildContext context, bool isVisible) builder,
    double visibilityThreshold = 0.1,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return NotificationListener<ScrollNotification>(
          onNotification: (notification) {
            // Handle viewport visibility changes
            return false;
          },
          child: Builder(
            builder: (context) {
              // Simple visibility check - in production, you'd want more sophisticated logic
              final isVisible = constraints.maxHeight > 0;
              return builder(context, isVisible);
            },
          ),
        );
      },
    );
  }

  /// Creates an optimized stack with better performance
  static Widget optimizedStack({
    Key? key,
    AlignmentGeometry alignment = AlignmentDirectional.topStart,
    TextDirection? textDirection,
    StackFit fit = StackFit.loose,
    Clip clipBehavior = Clip.hardEdge,
    required List<Widget> children,
  }) {
    return optimizedRepaintBoundary(
      child: Stack(
        key: key,
        alignment: alignment,
        textDirection: textDirection,
        fit: fit,
        clipBehavior: clipBehavior,
        children: children.map((child) {
          return optimizedRepaintBoundary(child: child);
        }).toList(),
      ),
    );
  }

  /// Memory-efficient widget cache for expensive widgets
  static final Map<String, Widget> _widgetCache = {};

  /// Cached widget builder for expensive components
  static Widget cachedWidget({
    required String cacheKey,
    required Widget Function() builder,
    Duration? cacheExpiry,
  }) {
    if (_widgetCache.containsKey(cacheKey)) {
      return _widgetCache[cacheKey]!;
    }

    final widget = builder();
    _widgetCache[cacheKey] = widget;

    // Auto-expire cache if needed
    if (cacheExpiry != null) {
      Future.delayed(cacheExpiry, () {
        _widgetCache.remove(cacheKey);
      });
    }

    return widget;
  }

  /// Clears the widget cache
  static void clearWidgetCache() {
    _widgetCache.clear();
  }
}

/// Mixin for widgets that need advanced performance optimization
mixin WidgetPerformanceOptimization<T extends StatefulWidget> on State<T> {
  bool _mounted = false;

  @override
  void initState() {
    super.initState();
    _mounted = true;
  }

  @override
  void dispose() {
    _mounted = false;
    super.dispose();
  }

  /// Safe setState that checks if widget is still mounted
  void safeSetState(VoidCallback fn) {
    if (_mounted && mounted) {
      setState(fn);
    }
  }

  /// Debounced setState for high-frequency updates
  Timer? _debounceTimer;
  void debouncedSetState(VoidCallback fn,
      {Duration delay = const Duration(milliseconds: 100)}) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, () {
      safeSetState(fn);
    });
  }

  @override
  void setState(VoidCallback fn) {
    safeSetState(fn);
  }
}

/// Advanced lazy loading widget for expensive content
class LazyLoadingWidget extends StatefulWidget {
  final Widget Function() builder;
  final Widget placeholder;
  final Duration delay;
  final bool enableCache;
  final String? cacheKey;

  const LazyLoadingWidget({
    Key? key,
    required this.builder,
    required this.placeholder,
    this.delay = const Duration(milliseconds: 100),
    this.enableCache = false,
    this.cacheKey,
  }) : super(key: key);

  @override
  State<LazyLoadingWidget> createState() => _LazyLoadingWidgetState();
}

class _LazyLoadingWidgetState extends State<LazyLoadingWidget>
    with WidgetPerformanceOptimization {
  Widget? _cachedWidget;
  bool _isLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadWidget();
  }

  Future<void> _loadWidget() async {
    await Future.delayed(widget.delay);
    if (!mounted) return;

    if (widget.enableCache && widget.cacheKey != null) {
      _cachedWidget = WidgetOptimizationUtils.cachedWidget(
        cacheKey: widget.cacheKey!,
        builder: widget.builder,
      );
    } else {
      _cachedWidget = widget.builder();
    }

    safeSetState(() {
      _isLoaded = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoaded && _cachedWidget != null) {
      return WidgetOptimizationUtils.optimizedRepaintBoundary(
        child: _cachedWidget!,
      );
    }
    return widget.placeholder;
  }
}