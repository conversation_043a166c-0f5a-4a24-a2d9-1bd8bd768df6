import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../controllers/profile_controller.dart';
import '../models/lesson_model.dart';
import '../models/quiz_model.dart';
import '../res/style.dart';

class FavLessonIconButton extends StatefulWidget {
  final LessonModel lesson;

  const FavLessonIconButton({super.key, required this.lesson});

  @override
  State<FavLessonIconButton> createState() => _FavLessonIconButtonState();
}

class _FavLessonIconButtonState extends State<FavLessonIconButton> {
  final ProfileController profileController = Get.find();
  bool _isFav = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkIfLessonIsFav(); // Check if the lesson is Fav on init
  }

  // Check if the lesson is already Fav in Firestore
  Future<void> _checkIfLessonIsFav() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true; // Show loading state while checking Firestore
    });
    try {
      final favLessonSnapshot = await firestore
          .collection('users')
          .doc(profileController.userr.value.uid)
          .collection('favLessons')
          .doc(widget
              .lesson.lessonId) // Assuming each lesson has a unique lessonId
          .get();

      if (!mounted) return;

      _isFav = favLessonSnapshot.exists;
      _isLoading = false;
    } catch (e) {
      if (!mounted) return;
      _isLoading = false;
    }

    if (mounted) {
      setState(() {});
    }
  }

  // Toggle the save state of the lesson
  void _toggleLessonFav() async {
    if (!mounted) return;

    if (_isFav) {
      // If already Fav, remove from Firestore
      profileController.deleteFromFavLesson(
          favLessonId: widget.lesson.lessonId!);
    } else {
      // If not Fav, add to Firestore
      profileController.addToFavLesson(favLesson: widget.lesson);
    }

    // Immediately toggle the local state for a responsive UI update
    if (mounted) {
      setState(() {
        _isFav = !_isFav;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // While checking Firestore, show loading state
    if (_isLoading) {
      return IconButton(
        icon: SvgPicture.asset('assets/svgs/heart_outlined_icon.svg'),
        onPressed: null, // Disable button while loading
      );
    }

    // Show the appropriate icon based on whether the lesson is Fav
    return IconButton(
      padding: const EdgeInsets.all(0),
      onPressed: _toggleLessonFav, // Toggle the Fav status
      icon: _isFav
          ? SvgPicture.asset('assets/svgs/heart_filled_icon.svg')
          : SvgPicture.asset('assets/svgs/heart_outlined_icon.svg'),
    );
  }
}

class FavQuizIconButton extends StatefulWidget {
  final QuizModel quiz;

  const FavQuizIconButton({super.key, required this.quiz});

  @override
  State<FavQuizIconButton> createState() => _FavQuizIconButtonState();
}

class _FavQuizIconButtonState extends State<FavQuizIconButton> {
  final ProfileController profileController = Get.find();
  bool _isFav = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkIfQuizIsFav(); // Check if the quiz is Fav on init
  }

  // Check if the quiz is already Fav in Firestore
  Future<void> _checkIfQuizIsFav() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true; // Show loading state while checking Firestore
    });
    try {
      final favQuizSnapshot = await firestore
          .collection('users')
          .doc(profileController.userr.value.uid)
          .collection('favQuizzes')
          .doc(widget.quiz.quizId) // Assuming each quiz has a unique quizId
          .get();

      if (!mounted) return;

      _isFav = favQuizSnapshot.exists;
      _isLoading = false;
    } catch (e) {
      if (!mounted) return;
      _isLoading = false;
    }

    if (mounted) {
      setState(() {});
    }
  }

  // Toggle the save state of the quiz
  void _toggleQuizFav() async {
    if (!mounted) return;

    if (_isFav) {
      // If already Fav, remove from Firestore
      profileController.deleteFromFavQuizes(favQuizId: widget.quiz.quizId!);
    } else {
      // If not Fav, add to Firestore
      profileController.addToFavQuizes(favQuiz: widget.quiz);
    }

    // Immediately toggle the local state for a responsive UI update
    if (mounted) {
      setState(() {
        _isFav = !_isFav;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // While checking Firestore, show loading state
    if (_isLoading) {
      return IconButton(
        icon: SvgPicture.asset('assets/svgs/heart_outlined_icon.svg'),
        onPressed: null, // Disable button while loading
      );
    }

    // Show the appropriate icon based on whether the quiz is Fav
    return IconButton(
        padding: const EdgeInsets.all(0),
        onPressed: _toggleQuizFav, // Toggle the Fav status
        icon: _isFav
            ? SvgPicture.asset('assets/svgs/heart_filled_icon.svg')
            : SvgPicture.asset('assets/svgs/heart_outlined_icon.svg'));
  }
}

class FavShuffleQuizIconButton extends StatefulWidget {
  final ShuffleQuizModel quiz;

  const FavShuffleQuizIconButton({super.key, required this.quiz});

  @override
  State<FavShuffleQuizIconButton> createState() =>
      _FavShuffleQuizIconButtonState();
}

class _FavShuffleQuizIconButtonState extends State<FavShuffleQuizIconButton> {
  final ProfileController profileController = Get.find();
  bool _isFav = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkIfQuizIsFav(); // Check if the quiz is Fav on init
  }

  // Check if the quiz is already Fav in Firestore
  Future<void> _checkIfQuizIsFav() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true; // Show loading state while checking Firestore
    });
    try {
      final favQuizSnapshot = await firestore
          .collection('users')
          .doc(profileController.userr.value.uid)
          .collection('favShuffleQuizzes')
          .doc(widget.quiz.quizId) // Assuming each quiz has a unique quizId
          .get();

      if (!mounted) return;

      _isFav = favQuizSnapshot.exists; // Update the state based on existence
      _isLoading = false;
    } catch (e) {
      if (!mounted) return;
      _isLoading = false;
    }

    if (mounted) {
      setState(() {});
    }
  }

  // Toggle the save state of the quiz
  void _toggleQuizFav() async {
    if (!mounted) return;

    if (_isFav) {
      // If already Fav, remove from Firestore
      profileController.deleteFromFavShuffleQuizes(
          favQuizId: widget.quiz.quizId!);
    } else {
      // If not Fav, add to Firestore
      profileController.addToFavShuffleQuizes(favQuiz: widget.quiz);
    }

    // Immediately toggle the local state for a responsive UI update
    if (mounted) {
      setState(() {
        _isFav = !_isFav;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // While checking Firestore, show loading state
    if (_isLoading) {
      return IconButton(
        icon: SvgPicture.asset('assets/svgs/heart_outlined_icon.svg'),
        onPressed: null, // Disable button while loading
      );
    }

    // Show the appropriate icon based on whether the quiz is Fav
    return IconButton(
      padding: const EdgeInsets.all(0),
      onPressed: _toggleQuizFav, // Toggle the Fav status
      icon: _isFav
          ? SvgPicture.asset('assets/svgs/heart_filled_icon.svg')
          : SvgPicture.asset('assets/svgs/heart_outlined_icon.svg'),
    );
  }
}
