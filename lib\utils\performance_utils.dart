import 'package:flutter/foundation.dart';

import 'package:flutter/scheduler.dart';

/// Performance utilities for monitoring and optimizing app performance
class PerformanceUtils {
  static final _performanceOverlay = ValueNotifier<bool>(false);

  /// Enable performance overlay in debug mode
  static void togglePerformanceOverlay() {
    if (kDebugMode) {
      _performanceOverlay.value = !_performanceOverlay.value;
    }
  }

  /// Check if we're dropping frames
  static void monitorFrameRate(Function(double fps) onFpsUpdate) {
    if (!kDebugMode) return;

    int frameCount = 0;
    Duration totalElapsed = Duration.zero;
    Duration lastTimestamp = Duration.zero;

    SchedulerBinding.instance.addPersistentFrameCallback((timeStamp) {
      if (lastTimestamp == Duration.zero) {
        lastTimestamp = timeStamp;
        return;
      }

      frameCount++;
      totalElapsed += timeStamp - lastTimestamp;
      lastTimestamp = timeStamp;

      // Calculate FPS every 60 frames
      if (frameCount >= 60) {
        final fps = (frameCount * 1000000) / totalElapsed.inMicroseconds;
        onFpsUpdate(fps);
        frameCount = 0;
        totalElapsed = Duration.zero;
      }
    });
  }

  /// Defer heavy operations to next frame
  static Future<void> deferToNextFrame(VoidCallback callback) {
    return SchedulerBinding.instance.endOfFrame.then((_) {
      if (SchedulerBinding.instance.schedulerPhase == SchedulerPhase.idle) {
        callback();
      } else {
        SchedulerBinding.instance.addPostFrameCallback((_) => callback());
      }
    });
  }

  /// Run expensive operation without blocking UI
  static Future<T> runExpensiveOperation<T>(
      Future<T> Function() operation) async {
    // For web, just run the operation
    if (kIsWeb) {
      return await operation();
    }

    // For mobile/desktop, run in isolate if possible
    try {
      return await compute((_) => operation(), null);
    } catch (e) {
      // Fallback to direct execution if compute fails
      return await operation();
    }
  }

  /// Measure operation duration
  static Future<T> measureOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await operation();
      stopwatch.stop();
      debugPrint(
          '✅ $operationName completed in ${stopwatch.elapsedMilliseconds}ms');
      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint(
          '❌ $operationName failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      rethrow;
    }
  }
}
