import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../utils/enhanced_cache_manager.dart';
import '../res/style.dart';

/// Ultra-fast image widget with instant loading and zero layout shifts
class UltraFastImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final bool enableHeroAnimation;
  final String? heroTag;
  final VoidCallback? onTap;

  const UltraFastImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.enableHeroAnimation = false,
    this.heroTag,
    this.onTap,
  }) : super(key: key);

  @override
  State<UltraFastImage> createState() => _UltraFastImageState();
}

class _UltraFastImageState extends State<UltraFastImage>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  File? _cachedFile;
  bool _isLoaded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150), // Super fast fade
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );

    _loadImageInstantly();
  }

  Future<void> _loadImageInstantly() async {
    try {
      // Try to get cached image first
      final cachedFile =
          await EnhancedCacheManager.getInstantImage(widget.imageUrl);

      if (cachedFile != null && mounted) {
        setState(() {
          _cachedFile = cachedFile;
          _isLoaded = true;
        });
        _controller.forward();
        return;
      }
    } catch (e) {
      debugPrint('Error loading cached image: $e');
    }
    
    // If not cached, start preloading
    _preloadImage();
  }

  Future<void> _preloadImage() async {
    try {
      final file = await EnhancedCacheManager.ultraFastPreload(widget.imageUrl);

      if (file != null && mounted) {
        setState(() {
          _cachedFile = file;
          _isLoaded = true;
        });
        _controller.forward();
      } else if (mounted) {
        // Fallback to placeholder if preloading fails
        debugPrint('Failed to preload image: ${widget.imageUrl}');
      }
    } catch (e) {
      if (mounted) {
        debugPrint('Error preloading image: $e');
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget _buildImage() {
    if (_cachedFile != null) {
      return Image.file(
        _cachedFile!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        gaplessPlayback: true, // Prevents flicker
        filterQuality: FilterQuality.medium, // Balanced quality/performance
      );
    }

    // Fallback to cached network image for edge cases
    return CachedNetworkImage(
      imageUrl: widget.imageUrl,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      cacheManager: EnhancedCacheManager.instance,
      fadeInDuration: const Duration(milliseconds: 100),
      fadeOutDuration: const Duration(milliseconds: 100),
      placeholder: (context, url) => _buildPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: mainColor.withValues(alpha: 0.1),
        borderRadius: widget.borderRadius ?? BorderRadius.circular(14),
      ),
      child: const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(mainColor),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: widget.borderRadius ?? BorderRadius.circular(14),
      ),
      child: const Icon(
        Icons.image_not_supported_outlined,
        color: Colors.grey,
        size: 32,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = ClipRRect(
      borderRadius: widget.borderRadius ?? BorderRadius.circular(14),
      child: SizedBox(
        width: widget.width,
        height: widget.height,
        child: _isLoaded
            ? FadeTransition(
                opacity: _fadeAnimation,
                child: _buildImage(),
              )
            : _buildPlaceholder(),
      ),
    );

    // Add hero animation if enabled
    if (widget.enableHeroAnimation && widget.heroTag != null) {
      imageWidget = Hero(
        tag: widget.heroTag!,
        child: imageWidget,
      );
    }

    // Add tap functionality if provided
    if (widget.onTap != null) {
      imageWidget = GestureDetector(
        onTap: widget.onTap,
        child: imageWidget,
      );
    }

    return imageWidget;
  }
}

/// Ultra-fast image preloader for batch operations
class UltraFastImagePreloader {
  static final Map<String, bool> _preloadedUrls = {};
  static final Map<String, DateTime> _preloadTimestamps = {};

  /// Preload images for a specific screen/widget
  static Future<void> preloadForScreen(
    List<String> imageUrls, {
    String? screenName,
    int concurrency = 4, // Reduced for better stability
  }) async {
    // Filter out already preloaded URLs and expired ones
    final urlsToPreload = imageUrls.where((url) {
      if (!_preloadedUrls.containsKey(url)) return true;
      
      // Check if preload is expired (older than 1 hour)
      final timestamp = _preloadTimestamps[url];
      if (timestamp != null) {
        final isExpired = DateTime.now().difference(timestamp).inHours > 1;
        if (isExpired) {
          _preloadedUrls.remove(url);
          _preloadTimestamps.remove(url);
          return true;
        }
      }
      return false;
    }).toList();

    if (urlsToPreload.isEmpty) {
      debugPrint('📱 All images already cached for $screenName');
      return;
    }

    debugPrint('🚀 Preloading ${urlsToPreload.length} images for $screenName');

    await EnhancedCacheManager.batchUltraPreload(
      urlsToPreload,
      concurrency: concurrency,
    );

    // Mark as preloaded with timestamp
    final now = DateTime.now();
    for (final url in urlsToPreload) {
      _preloadedUrls[url] = true;
      _preloadTimestamps[url] = now;
    }

    debugPrint('✅ Preloading completed for $screenName');
  }

  /// Clear preload tracking (call when memory pressure is high)
  static void clearPreloadTracking() {
    _preloadedUrls.clear();
    _preloadTimestamps.clear();
    debugPrint('🧹 Preload tracking cleared');
  }

  /// Check if URL is already preloaded
  static bool isPreloaded(String url) {
    return _preloadedUrls.containsKey(url);
  }

  /// Clear expired preload entries
  static void clearExpiredEntries() {
    final now = DateTime.now();
    final expiredUrls = <String>[];
    
    _preloadTimestamps.forEach((url, timestamp) {
      if (now.difference(timestamp).inHours > 24) {
        expiredUrls.add(url);
      }
    });
    
    for (final url in expiredUrls) {
      _preloadedUrls.remove(url);
      _preloadTimestamps.remove(url);
    }
    
    if (expiredUrls.isNotEmpty) {
      debugPrint('🗑️ Cleared ${expiredUrls.length} expired preload entries');
    }
  }

  /// Get preload statistics
  static Map<String, dynamic> getPreloadStats() {
    return {
      'totalPreloaded': _preloadedUrls.length,
      'memoryUsage': CacheMemoryManager.getMemoryUsageString(),
      'oldestEntry': _preloadTimestamps.values.isEmpty 
          ? null 
          : _preloadTimestamps.values.reduce((a, b) => a.isBefore(b) ? a : b),
    };
  }
}
