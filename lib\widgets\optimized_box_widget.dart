import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/models/lesson_model.dart';
import 'package:bibl/models/quiz_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/services/lessonquiz_completion_service.dart';
import 'package:bibl/services/share_item_service.dart';
import 'package:bibl/shuffle_quiz.dart';
import 'package:bibl/lesson_intro.dart';
import 'package:bibl/quiz.dart';
import 'package:bibl/utils/widget_optimization_utils.dart';
import 'package:bibl/utils/premium_animations.dart';
import 'package:bibl/widgets/boxtile_category_image_widget.dart';
import 'package:bibl/widgets/fav_icon_widget.dart';
import 'package:bibl/widgets/image_carousel_widget.dart';
import 'package:bibl/widgets/rewarded_ads_dialog.dart';
import 'package:bibl/widgets/saved_icon_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

/// Optimized BoxWidget with premium performance and animations
class OptimizedBoxWidget extends StatefulWidget {
  final LessonModel? lesson;
  final QuizModel? quiz;
  final ShuffleQuizModel? shuffleQuiz;
  final VoidCallback? onTap;
  final bool enableAnimation;
  final int index;

  const OptimizedBoxWidget({
    Key? key,
    this.lesson,
    this.quiz,
    this.shuffleQuiz,
    this.onTap,
    this.enableAnimation = true,
    this.index = 0,
  }) : super(key: key);

  @override
  State<OptimizedBoxWidget> createState() => _OptimizedBoxWidgetState();
}

class _OptimizedBoxWidgetState extends State<OptimizedBoxWidget>
    with WidgetPerformanceOptimization, AutomaticKeepAliveClientMixin {
  
  // Cache controllers to avoid repeated Get.find() calls
  late final ProfileController _profileController;
  late final LessonQuizCompletionService _completionService;
  late final ShareProductService _shareService;
  
  // Cache computed values
  String? _cachedId;
  String? _cachedName;
  String? _cachedCategory;
  bool? _cachedIsCompleted;
  Widget? _cachedContent;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _profileController = Get.find<ProfileController>();
    _completionService = LessonQuizCompletionService();
    _shareService = ShareProductService();
    _precomputeValues();
  }

  /// Precompute values to avoid repeated calculations
  void _precomputeValues() {
    if (widget.lesson != null) {
      _cachedId = widget.lesson!.lessonId;
      _cachedName = widget.lesson!.lessonName ?? '';
      _cachedCategory = widget.lesson!.category ?? '';
      _cachedIsCompleted = _profileController
              .userr.value.completedLessonQuizesInThirtyDays
              ?.containsKey(_cachedId!) ??
          false;
    } else if (widget.quiz != null) {
      _cachedId = widget.quiz!.quizId;
      _cachedName = widget.quiz!.quizName ?? '';
      _cachedCategory = widget.quiz!.category ?? '';
      _cachedIsCompleted = _profileController
              .userr.value.completedLessonQuizesInThirtyDays
              ?.containsKey(_cachedId!) ??
          false;
    } else if (widget.shuffleQuiz != null) {
      _cachedId = widget.shuffleQuiz!.quizId;
      _cachedName = widget.shuffleQuiz!.quizName ?? '';
      _cachedCategory = widget.shuffleQuiz!.category ?? '';
      _cachedIsCompleted = _profileController
              .userr.value.completedLessonQuizesInThirtyDays
              ?.containsKey(_cachedId!) ??
          false;
    }
  }

  /// Optimized navigation with premium handling
  void _onItemTap() {
    Widget targetScreen;
    
    if (widget.lesson != null) {
      targetScreen = LessonIntro(lesson: widget.lesson!);
    } else if (widget.quiz != null) {
      targetScreen = Quiz(quiz: widget.quiz!);
    } else if (widget.shuffleQuiz != null) {
      targetScreen = ShuffleQuiz(quiz: widget.shuffleQuiz!);
    } else {
      return;
    }

    if (!(_profileController.userr.value.isPremiumUser ?? false)) {
      final isOpenedWithin30Days = _completionService.isOpenedWithinThirtyDays(
        _cachedId!,
        _profileController.userr.value.openedQuizesAndArticlesinMonth,
      );

      if (isOpenedWithin30Days || (_cachedIsCompleted ?? false)) {
        Get.to(() => targetScreen);
      } else {
        if ((_profileController.userr.value.hearts ?? 0) == 0) {
          Get.dialog(RewardedAdsDialog(classs: targetScreen));
        } else {
          Get.to(() => targetScreen);
          final currentHearts = _profileController.userr.value.hearts! - 1;
          _profileController.userr.value.hearts = currentHearts;
          _profileController.updateHearts(currentHearts);
        }
      }
      _profileController.userr.refresh();
    } else {
      Get.to(() => targetScreen);
    }

    widget.onTap?.call();
  }

  /// Build optimized content based on item type
  Widget _buildOptimizedContent() {
    if (_cachedContent != null) {
      return _cachedContent!;
    }

    Widget content;
    if (widget.lesson != null) {
      content = _buildLessonContent();
    } else if (widget.quiz != null) {
      content = _buildQuizContent();
    } else if (widget.shuffleQuiz != null) {
      content = _buildShuffleQuizContent();
    } else {
      content = const SizedBox.shrink();
    }

    _cachedContent = content;
    return content;
  }

  Widget _buildLessonContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeaderRow(),
        const SizedBox(height: 16),
        _buildImageCarousel(),
        const SizedBox(height: 12),
        _buildActionRow(),
      ],
    );
  }

  Widget _buildQuizContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeaderRow(),
        const SizedBox(height: 16),
        _buildImageCarousel(),
        const SizedBox(height: 12),
        _buildActionRow(),
      ],
    );
  }

  Widget _buildShuffleQuizContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeaderRow(isShuffleQuiz: true),
        const SizedBox(height: 16),
        _buildImageCarousel(),
        const SizedBox(height: 12),
        _buildActionRow(),
      ],
    );
  }

  Widget _buildHeaderRow({bool isShuffleQuiz = false}) {
    return WidgetOptimizationUtils.optimizedRepaintBoundary(
      debugLabel: 'header_row_${widget.index}',
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category image with lazy loading
          LazyLoadingWidget(
            builder: () => boxTileCategoryImageBuilder(_cachedCategory ?? ''),
            placeholder: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            delay: const Duration(milliseconds: 50),
            enableCache: true,
            cacheKey: 'category_$_cachedCategory',
          ),
          const SizedBox(width: 16),
          
          // Expandable content area
          Expanded(
            child: GestureDetector(
              onTap: _onItemTap,
              child: WidgetOptimizationUtils.optimizedRepaintBoundary(
                debugLabel: 'content_area_${widget.index}',
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    WidgetOptimizationUtils.optimizedText(
                      _cachedName ?? '',
                      maxLines: 5,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    WidgetOptimizationUtils.optimizedText(
                      isShuffleQuiz ? 'Shuffle Kviz' : _getItemTypeText(),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.normal,
                        color: grey2Color,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Saved icon (only show if user is logged in)
          if (_profileController.userr.value.uid != null)
            _buildSavedIcon(),
        ],
      ),
    );
  }

  Widget _buildSavedIcon() {
    return WidgetOptimizationUtils.optimizedRepaintBoundary(
      debugLabel: 'saved_icon_${widget.index}',
      child: LazyLoadingWidget(
        builder: () {
          if (widget.lesson != null) {
            return SavedLessonIconButton(lesson: widget.lesson!);
          } else if (widget.quiz != null) {
            return SavedQuizIconButton(quiz: widget.quiz!);
          } else if (widget.shuffleQuiz != null) {
            return SavedShuffleQuizIconButton(quiz: widget.shuffleQuiz!);
          }
          return const SizedBox.shrink();
        },
        placeholder: Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        delay: const Duration(milliseconds: 100),
      ),
    );
  }

  Widget _buildImageCarousel() {
    return WidgetOptimizationUtils.optimizedRepaintBoundary(
      debugLabel: 'image_carousel_${widget.index}',
      child: GestureDetector(
        onTap: _onItemTap,
        child: LazyLoadingWidget(
          builder: () => MyImageCarousel(
            lesson: widget.lesson,
            quiz: widget.quiz,
            shuffleQuiz: widget.shuffleQuiz,
          ),
          placeholder: Container(
            height: 160,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Icon(
                Icons.image,
                color: Colors.grey,
                size: 40,
              ),
            ),
          ),
          delay: const Duration(milliseconds: 150),
        ),
      ),
    );
  }

  Widget _buildActionRow() {
    return WidgetOptimizationUtils.optimizedRepaintBoundary(
      debugLabel: 'action_row_${widget.index}',
      child: Row(
        children: [
          // Favorite icon
          if (_profileController.userr.value.uid != null)
            LazyLoadingWidget(
              builder: () => _buildFavoriteIcon(),
              placeholder: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              delay: const Duration(milliseconds: 200),
            ),
          
          const SizedBox(width: 12),
          
          // Share button with premium animation
          PremiumButton(
            onPressed: () => _shareService.shareProduct(
              _cachedId!,
              _getShareType(),
            ),
            backgroundColor: Colors.transparent,
            elevation: 0,
            padding: const EdgeInsets.all(8),
            child: SvgPicture.asset(
              'assets/svgs/share_icon.svg',
              width: 20,
              height: 20,
            ),
          ),
          
          const Spacer(),
          
          // Completion indicator
          if (_cachedIsCompleted ?? false)
            PremiumAnimations.scaleIn(
              duration: const Duration(milliseconds: 400),
              child: const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 24,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFavoriteIcon() {
    if (widget.lesson != null) {
      return FavLessonIconButton(lesson: widget.lesson!);
    } else if (widget.quiz != null) {
      return FavQuizIconButton(quiz: widget.quiz!);
    } else if (widget.shuffleQuiz != null) {
      return FavShuffleQuizIconButton(quiz: widget.shuffleQuiz!);
    }
    return const SizedBox.shrink();
  }

  String _getItemTypeText() {
    if (widget.lesson != null) return 'Lekcija';
    if (widget.quiz != null) return 'Kviz';
    return 'Sadržaj';
  }

  String _getShareType() {
    if (widget.lesson != null) return 'lesson';
    if (widget.quiz != null) return 'quiz';
    if (widget.shuffleQuiz != null) return 'shuffleQuiz';
    return 'content';
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final baseWidget = WidgetOptimizationUtils.optimizedContainer(
      key: ValueKey('box_widget_${_cachedId}_${widget.index}'),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _buildOptimizedContent(),
      ),
    );

    if (widget.enableAnimation) {
      return PremiumAnimations.fadeSlideIn(
        duration: const Duration(milliseconds: 400),
        delay: Duration(milliseconds: widget.index * 50),
        child: baseWidget,
      );
    }

    return baseWidget;
  }

  @override
  void dispose() {
    _cachedContent = null;
    super.dispose();
  }
}

/// Optimized BoxWidget factory for different item types
class BoxWidgetFactory {
  static Widget createOptimized({
    LessonModel? lesson,
    QuizModel? quiz,
    ShuffleQuizModel? shuffleQuiz,
    VoidCallback? onTap,
    bool enableAnimation = true,
    int index = 0,
  }) {
    return OptimizedBoxWidget(
      lesson: lesson,
      quiz: quiz,
      shuffleQuiz: shuffleQuiz,
      onTap: onTap,
      enableAnimation: enableAnimation,
      index: index,
    );
  }

  /// Create a list of optimized box widgets
  static List<Widget> createOptimizedList({
    required List<dynamic> items,
    bool enableAnimation = true,
    VoidCallback? onItemTap,
  }) {
    return List.generate(items.length, (index) {
      final item = items[index];
      
      if (item is LessonModel) {
        return createOptimized(
          lesson: item,
          index: index,
          enableAnimation: enableAnimation,
          onTap: onItemTap,
        );
      } else if (item is QuizModel) {
        return createOptimized(
          quiz: item,
          index: index,
          enableAnimation: enableAnimation,
          onTap: onItemTap,
        );
      } else if (item is ShuffleQuizModel) {
        return createOptimized(
          shuffleQuiz: item,
          index: index,
          enableAnimation: enableAnimation,
          onTap: onItemTap,
        );
      }
      
      return const SizedBox.shrink();
    });
  }
}