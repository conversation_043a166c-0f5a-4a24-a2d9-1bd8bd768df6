import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  String? name;
  int? appOpenCount;
  Timestamp? lastPopupShownTimestamp;
  String? surname;
  String? uniqueName;

  String? email;
  String? profilePhoto;
  String? uid;
  int? neurons;
  int? lastMilestone;
  int? correctQuizAnswersCounter;
  Map<String, dynamic>? completedLessonQuizesInThirtyDays;
  int? weeklyStreak;
  int? consecutiveStreak;
  int? hearts;
  bool? isPremiumUser;
  bool? isNotificationOn;
  bool? isSoundOn;
  bool? hasSeenOnboadingScreen;
  bool? hasSeenInfoSheet;
  String? title;
  String? lastInactivityNotification;
  List<String>? achievements;
  List<String>? listOfFavCategories;
  List<String>? listOfLibraryCategories;
  Timestamp? lastActiveTime;
  Timestamp? lastStreakUpdate;
  String? deviceToken;
  Timestamp? lastRewardLessonMarathonerTimestamp;
  String? league;
  String? groupId;

  List<String>? completedQuizIds;
  List<String>? completedArticleIds;
  List<String>? openedArticleIds;

  int? totalTimeSpent;

  int? consecutivePerfectQuizzes;

  int? leagueWinsCount;
  int? leagueEventsCounter;

  Map<String, dynamic>? openedQuizesAndArticlesinMonth;
  List<String>? completedQuizesOfMythologyCategory;
  List<String>? completedQuizesOfCosmosCategory;
  List<String>? completedQuizesOfHistoryCategory;
  List<String>? completedQuizesOfScienceCategory;
  List<String>? completedQuizesOfArtCategory;

  // New fields for multi-category quizzes
  List<String>? listOfMultiCategoryQuizes;
  Timestamp? multiCategoryLastRewardedAt;
  Timestamp? lastListResetTimestamp;

  // New fields for league milestones
  bool? hasReachedSilverLeague;
  bool? hasReachedGoldLeague;

  // New field for 14-day streak count
  int? completedQuizCountDailyFourteenDayStreakCount;

  // Additional achievement tracking fields
  int? loyalUserStreakDays; // For 90-day streak tracking
  int? perfectQuizStreak; // For 5 consecutive perfect quizzes
  Timestamp? lastDailyQuizCompletion; // For consistent learner achievement

  // Weekly XP tracking fields
  int? weeklyScore;
  Timestamp? lastWeeklyReset;

  // Achievement timing tracking fields
  Timestamp? lastDailyLearnerReward; // For monthly Daily Learner achievement
  Timestamp? lastWeeklyStreakReward; // For weekly Weekly Streak achievement

  UserModel({
    this.name,
    this.surname,
    this.uniqueName,
    this.groupId,
    this.appOpenCount = 0,
    this.lastPopupShownTimestamp,
    this.lastInactivityNotification,
    this.email,
    this.profilePhoto,
    this.hasSeenOnboadingScreen,
    this.hasSeenInfoSheet,
    this.lastRewardLessonMarathonerTimestamp,
    this.isPremiumUser,
    this.uid,
    this.neurons,
    this.weeklyStreak,
    this.consecutiveStreak,
    this.hearts,
    this.isNotificationOn,
    this.isSoundOn,
    this.title,
    this.achievements,
    this.listOfFavCategories,
    this.listOfLibraryCategories,
    this.lastActiveTime,
    this.lastStreakUpdate,
    this.deviceToken,
    this.openedArticleIds = const [],
    this.completedQuizIds = const [],
    this.completedArticleIds = const [],
    this.league,
    this.totalTimeSpent = 0,
    this.lastMilestone = 0,
    this.consecutivePerfectQuizzes = 0,
    this.correctQuizAnswersCounter = 0,
    this.leagueWinsCount = 0,
    this.leagueEventsCounter = 0,
    this.openedQuizesAndArticlesinMonth = const {},
    this.completedQuizesOfMythologyCategory = const [],
    this.completedQuizesOfCosmosCategory = const [],
    this.completedQuizesOfHistoryCategory = const [],
    this.completedQuizesOfScienceCategory = const [],
    this.completedQuizesOfArtCategory = const [],
    this.listOfMultiCategoryQuizes = const [],
    this.multiCategoryLastRewardedAt,
    this.lastListResetTimestamp,
    this.hasReachedSilverLeague = false,
    this.hasReachedGoldLeague = false,
    this.completedQuizCountDailyFourteenDayStreakCount = 0,
    this.loyalUserStreakDays = 0,
    this.perfectQuizStreak = 0,
    this.lastDailyQuizCompletion,
    this.completedLessonQuizesInThirtyDays = const {},
    this.weeklyScore = 0,
    this.lastWeeklyReset,
    this.lastDailyLearnerReward,
    this.lastWeeklyStreakReward,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      name: json['name'],
      groupId: json['groupId'],
      appOpenCount: json['appOpenCount'] ?? 0,
      lastPopupShownTimestamp: json['lastPopupShownTimestamp'],
      hasSeenOnboadingScreen: json['hasSeenOnboadingScreen'],
      hasSeenInfoSheet: json['hasSeenInfoSheet'],
      surname: json['surname'],
      uniqueName: json['uniqueName'],
      email: json['email'],
      profilePhoto: json['profilePhoto'],
      uid: json['uid'],
      lastRewardLessonMarathonerTimestamp:
          json['lastRewardLessonMarathonerTimestamp'],
      neurons: json['neurons'] ?? 0,
      weeklyStreak: json['weeklyStreak'] ?? 0,
      consecutiveStreak: json['consecutiveStreak'] ?? 0,
      hearts: json['hearts'] ?? 0,
      isPremiumUser: json['isPremiumUser'],
      isNotificationOn: json['isNotificationOn'],
      isSoundOn: json['isSoundOn'],
      title: json['title'],
      achievements: List<String>.from(json['achievements'] ?? []),
      listOfFavCategories: List<String>.from(json['listOfFavCategories'] ?? []),
      listOfLibraryCategories:
          List<String>.from(json['listOfLibraryCategories'] ?? []),
      lastActiveTime: json['lastActiveTime'] ?? Timestamp.now(),
      lastStreakUpdate: json['lastStreakUpdate'],
      deviceToken: json['deviceToken'],
      completedQuizIds: List<String>.from(json['completedQuizIds'] ?? []),
      openedArticleIds: List<String>.from(json['openedArticleIds'] ?? []),
      completedArticleIds: List<String>.from(json['completedArticleIds'] ?? []),
      league: json['league'],
      totalTimeSpent: json['totalTimeSpent'] ?? 0,
      lastMilestone: json['lastMilestone'] ?? 0,
      consecutivePerfectQuizzes: json['consecutivePerfectQuizzes'] ?? 0,
      correctQuizAnswersCounter: json['correctQuizAnswersCounter'] ?? 0,
      leagueWinsCount: json['leagueWinsCount'] ?? 0,
      leagueEventsCounter: json['leagueEventsCounter'] ?? 0,
      completedQuizesOfMythologyCategory:
          List<String>.from(json['completedQuizesOfMythologyCategory'] ?? []),
      openedQuizesAndArticlesinMonth:
          json['openedQuizesAndArticlesinMonth'] ?? {},
      completedQuizesOfCosmosCategory:
          List<String>.from(json['completedQuizesOfCosmosCategory'] ?? []),
      completedQuizesOfHistoryCategory:
          List<String>.from(json['completedQuizesOfHistoryCategory'] ?? []),
      completedQuizesOfScienceCategory:
          List<String>.from(json['completedQuizesOfScienceCategory'] ?? []),
      completedQuizesOfArtCategory:
          List<String>.from(json['completedQuizesOfArtCategory'] ?? []),
      listOfMultiCategoryQuizes:
          List<String>.from(json['listOfMultiCategoryQuizes'] ?? []),
      multiCategoryLastRewardedAt: json['multiCategoryLastRewardedAt'],
      lastListResetTimestamp: json['lastListResetTimestamp'],
      hasReachedSilverLeague: json['hasReachedSilverLeague'] ?? false,
      hasReachedGoldLeague: json['hasReachedGoldLeague'] ?? false,
      completedQuizCountDailyFourteenDayStreakCount:
          json['completedQuizCountDailyFourteenDayStreakCount'] ?? 0,
      loyalUserStreakDays: json['loyalUserStreakDays'] ?? 0,
      perfectQuizStreak: json['perfectQuizStreak'] ?? 0,
      lastDailyQuizCompletion: json['lastDailyQuizCompletion'],
      completedLessonQuizesInThirtyDays:
          json['completedLessonQuizesInThirtyDays'] ?? {},
      weeklyScore: json['weeklyScore'] ?? 0,
      lastWeeklyReset: json['lastWeeklyReset'],
      lastDailyLearnerReward: json['lastDailyLearnerReward'],
      lastWeeklyStreakReward: json['lastWeeklyStreakReward'],
    );
  }

  Map<String, dynamic> toJson() => {
        "name": name,
        "groupId": groupId,
        "surname": surname,
        "uniqueName": uniqueName,
        "appOpenCount": appOpenCount,
        "lastPopupShownTimestamp": lastPopupShownTimestamp,
        "hasSeenOnboadingScreen": hasSeenOnboadingScreen,
        "hasSeenInfoSheet": hasSeenInfoSheet,
        "email": email,
        "uid": uid,
        "lastRewardLessonMarathonerTimestamp":
            lastRewardLessonMarathonerTimestamp,
        "profilePhoto": profilePhoto,
        "neurons": neurons,
        "weeklyStreak": weeklyStreak,
        "consecutiveStreak": consecutiveStreak,
        "hearts": hearts,
        "isPremiumUser": isPremiumUser,
        "isNotificationOn": isNotificationOn,
        "isSoundOn": isSoundOn,
        "title": title,
        "achievements": achievements,
        "listOfFavCategories": listOfFavCategories,
        "listOfLibraryCategories": listOfLibraryCategories,
        "lastActiveTime": lastActiveTime,
        "lastStreakUpdate": lastStreakUpdate,
        "deviceToken": deviceToken,
        "completedQuizIds": completedQuizIds,
        "openedArticleIds": openedArticleIds,
        "completedArticleIds": completedArticleIds,
        "league": league,
        "totalTimeSpent": totalTimeSpent,
        "lastMilestone": lastMilestone,
        "consecutivePerfectQuizzes": consecutivePerfectQuizzes,
        "correctQuizAnswersCounter": correctQuizAnswersCounter,
        "leagueWinsCount": leagueWinsCount,
        "leagueEventsCounter": leagueEventsCounter,
        "completedQuizesOfMythologyCategory":
            completedQuizesOfMythologyCategory,
        "openedQuizesAndArticlesinMonth": openedQuizesAndArticlesinMonth,
        "completedQuizesOfCosmosCategory": completedQuizesOfCosmosCategory,
        "completedQuizesOfHistoryCategory": completedQuizesOfHistoryCategory,
        "completedQuizesOfScienceCategory": completedQuizesOfScienceCategory,
        "completedQuizesOfArtCategory": completedQuizesOfArtCategory,
        "listOfMultiCategoryQuizes": listOfMultiCategoryQuizes,
        "multiCategoryLastRewardedAt": multiCategoryLastRewardedAt,
        "lastListResetTimestamp": lastListResetTimestamp,
        "hasReachedSilverLeague": hasReachedSilverLeague,
        "hasReachedGoldLeague": hasReachedGoldLeague,
        "completedQuizCountDailyFourteenDayStreakCount":
            completedQuizCountDailyFourteenDayStreakCount,
        "loyalUserStreakDays": loyalUserStreakDays,
        "perfectQuizStreak": perfectQuizStreak,
        "lastDailyQuizCompletion": lastDailyQuizCompletion,
        "completedLessonQuizesInThirtyDays": completedLessonQuizesInThirtyDays,
        "weeklyScore": weeklyScore,
        "lastWeeklyReset": lastWeeklyReset,
        "lastDailyLearnerReward": lastDailyLearnerReward,
        "lastWeeklyStreakReward": lastWeeklyStreakReward,
      };

  static UserModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return UserModel.fromJson(snapshot);
  }
}
