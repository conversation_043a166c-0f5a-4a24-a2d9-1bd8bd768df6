import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:bibl/utils/enhanced_cache_manager.dart';
import 'package:bibl/widgets/premium_shimmer.dart';

/// Optimized image widget with automatic caching and loading states
class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final Duration fadeInDuration;
  final Duration fadeOutDuration;
  final BlendMode? colorBlendMode;
  final Color? color;
  final FilterQuality filterQuality;
  final Map<String, String>? httpHeaders;

  const OptimizedImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.fadeOutDuration = const Duration(milliseconds: 300),
    this.colorBlendMode,
    this.color,
    this.filterQuality = FilterQuality.low,
    this.httpHeaders,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Check if it's a local file
    if (imageUrl.startsWith('file://') || imageUrl.startsWith('/')) {
      return _buildLocalImage();
    }

    // Check if it's an SVG
    if (imageUrl.endsWith('.svg')) {
      return _buildSvgImage();
    }

    // Network image with caching and WebP optimization
    return CachedNetworkImage(
      imageUrl: _getOptimizedImageUrl(),
      width: width,
      height: height,
      fit: fit,
      httpHeaders: _getOptimizedHeaders(),
      cacheManager: EnhancedCacheManager.instance,
      fadeInDuration: fadeInDuration,
      fadeOutDuration: fadeOutDuration,
      colorBlendMode: colorBlendMode,
      color: color,
      filterQuality: filterQuality,
      placeholder: (context, url) => _buildPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      imageBuilder: (context, imageProvider) => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: imageProvider,
            fit: fit,
            colorFilter: color != null
                ? ColorFilter.mode(color!, colorBlendMode ?? BlendMode.srcIn)
                : null,
          ),
        ),
      ),
    );
  }

  /// Get optimized image URL with WebP support
  String _getOptimizedImageUrl() {
    // For Firebase Storage URLs, try to get WebP version
    if (imageUrl.contains('firebasestorage.googleapis.com')) {
      // Try WebP version first (fallback to original if WebP fails)
      final webpUrl = imageUrl.replaceAll(RegExp(r'\.(jpg|jpeg|png)'), '.webp');
      return webpUrl;
    }

    return imageUrl;
  }

  /// Get optimized headers for better image loading
  Map<String, String> _getOptimizedHeaders() {
    final headers = <String, String>{
      'Accept': 'image/webp,image/avif,image/apng,image/*,*/*;q=0.8',
      'Cache-Control': 'max-age=31536000',
    };

    // Add custom headers if provided
    if (httpHeaders != null) {
      headers.addAll(httpHeaders!);
    }

    return headers;
  }

  Widget _buildLocalImage() {
    final file = File(imageUrl.replaceFirst('file://', ''));

    if (!file.existsSync()) {
      return _buildErrorWidget();
    }

    return Image.file(
      file,
      width: width,
      height: height,
      fit: fit,
      color: color,
      colorBlendMode: colorBlendMode,
      filterQuality: filterQuality,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          return child;
        }
        return AnimatedOpacity(
          opacity: frame == null ? 0 : 1,
          duration: fadeInDuration,
          curve: Curves.easeOut,
          child: child,
        );
      },
      errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
    );
  }

  Widget _buildSvgImage() {
    if (imageUrl.startsWith('http')) {
      return SvgPicture.network(
        imageUrl,
        width: width,
        height: height,
        fit: fit,
        colorFilter: color != null
            ? ColorFilter.mode(color!, colorBlendMode ?? BlendMode.srcIn)
            : null,
        placeholderBuilder: (context) => _buildPlaceholder(),
      );
    } else {
      return SvgPicture.asset(
        imageUrl,
        width: width,
        height: height,
        fit: fit,
        colorFilter: color != null
            ? ColorFilter.mode(color!, colorBlendMode ?? BlendMode.srcIn)
            : null,
        placeholderBuilder: (context) => _buildPlaceholder(),
      );
    }
  }

  Widget _buildPlaceholder() {
    return placeholder ??
        SizedBox(
          width: width,
          height: height,
          child: PremiumShimmer(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        );
  }

  Widget _buildErrorWidget() {
    return errorWidget ??
        Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.broken_image_outlined,
            color: Colors.grey.shade400,
            size: (width ?? 100) * 0.3,
          ),
        );
  }
}

/// Hero image widget for smooth transitions
class OptimizedHeroImage extends StatelessWidget {
  final String tag;
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;

  const OptimizedHeroImage({
    Key? key,
    required this.tag,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: tag,
      child: OptimizedImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit,
      ),
    );
  }
}
