plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}



def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}



android {
    namespace "com.umniLabNewApp.umniLabApp"
        compileSdkVersion 35
        ndkVersion "29.0.13113456"

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.umniLabNewApp.umniLabApp"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 23
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        
        // Performance optimizations
        vectorDrawables.useSupportLibrary = true
        renderscriptTargetApi 28
        renderscriptSupportModeEnabled true
    }

     signingConfigs {
       release {
           keyAlias keystoreProperties['keyAlias']
           keyPassword keystoreProperties['keyPassword']
           storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
           storePassword keystoreProperties['storePassword']
       }
   }
   buildTypes {
       debug {
           minifyEnabled false
           shrinkResources false
           debuggable true
       }
       release {
           minifyEnabled true  // Enable R8 code shrinking
           shrinkResources true  // Enable resource shrinking
           proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
           signingConfig signingConfigs.release

           // Additional optimizations
           ndk {
               debugSymbolLevel 'NONE'  // Remove debug symbols
           }
       }
   }
   
   // APK Splits - Create separate APKs for each architecture (disabled for debug)
   splits {
       abi {
           enable project.hasProperty('splitApk')
           reset()
           include 'armeabi-v7a', 'arm64-v8a', 'x86_64'
           universalApk false  // Don't create universal APK
       }
   }

}

flutter {
    source '../..'
}

dependencies {

   implementation 'androidx.multidex:multidex:2.0.1'
   implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
   coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'

    
}
