import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:bibl/controllers/auth_controller.dart';

class Splash extends StatefulWidget {
  const Splash({super.key});

  @override
  State<Splash> createState() => _SplashState();
}

class _SplashState extends State<Splash> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  bool _hasNavigated = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration:
          const Duration(milliseconds: 800), // Synced with navigation delay
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.8, curve: Curves.easeOutBack),
    ));

    // Start animation immediately
    _animationController.forward();

    // Navigate after animation completes
    _navigateAfterDelay();
  }

  void _navigateAfterDelay() {
    // Reduced delay for premium feel - wait for critical path only
    Future.delayed(const Duration(milliseconds: 800), () async {
      if (mounted && !_hasNavigated) {
        _hasNavigated = true;

        try {
          // Small delay for initialization
          await Future.delayed(const Duration(milliseconds: 500));

          // Get auth controller safely
          final authController = Get.find<AuthController>();

          // Enable auth redirect and trigger navigation
          authController.enableAuthRedirect();
          authController.loginRedirect(authController.user);
        } catch (e) {
          // Fallback navigation if something goes wrong
          debugPrint('Error during splash navigation: $e');
          Get.offAllNamed('/auth'); // Fallback route
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Image.asset(
                  'assets/images/splash.png',
                  fit: BoxFit.fill, // Fixed: use contain for better display
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
