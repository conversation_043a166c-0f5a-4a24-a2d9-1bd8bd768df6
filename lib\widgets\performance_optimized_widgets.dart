import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../utils/comprehensive_performance_manager.dart';

/// Optimized ListView that reduces rebuilds and memory usage
class OptimizedListView extends StatelessWidget {
  final int itemCount;
  final Widget Function(BuildContext, int) itemBuilder;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final double? itemExtent;
  final Widget? separatorBuilder;

  const OptimizedListView({
    Key? key,
    required this.itemCount,
    required this.itemBuilder,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.itemExtent,
    this.separatorBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use optimized list view with proper caching
    return ListView.separated(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics ?? const ClampingScrollPhysics(),
      // Optimize viewport for better performance
      cacheExtent: 200, // Cache 200 pixels outside viewport
      itemCount: itemCount,
      itemBuilder: (context, index) {
        // Wrap items with automatic disposal
        return _OptimizedListItem(
          key: ValueKey('item_$index'),
          child: itemBuilder(context, index),
        );
      },
      separatorBuilder: separatorBuilder != null 
          ? (context, index) => separatorBuilder!
          : (context, index) => const SizedBox.shrink(),
    );
  }
}

/// Optimized list item with automatic memory management
class _OptimizedListItem extends StatefulWidget {
  final Widget child;

  const _OptimizedListItem({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<_OptimizedListItem> createState() => _OptimizedListItemState();
}

class _OptimizedListItemState extends State<_OptimizedListItem>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => false; // Don't keep alive by default

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
}

/// Optimized text widget that prevents unnecessary rebuilds
class OptimizedText extends StatelessWidget {
  final String data;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final bool softWrap;

  const OptimizedText(
    this.data, {
    Key? key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.softWrap = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      data,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.ellipsis,
      softWrap: softWrap,
      // Optimize text rendering
      textWidthBasis: TextWidthBasis.longestLine,
      textHeightBehavior: const TextHeightBehavior(
        applyHeightToFirstAscent: false,
        applyHeightToLastDescent: false,
      ),
    );
  }
}

/// Optimized container with performance improvements
class OptimizedContainer extends StatelessWidget {
  final Widget? child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final Decoration? decoration;
  final AlignmentGeometry? alignment;

  const OptimizedContainer({
    Key? key,
    this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.color,
    this.decoration,
    this.alignment,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      alignment: alignment,
      decoration: decoration ??
          (color != null
              ? BoxDecoration(
                  color: color,
                  // Optimize for better rendering
                  borderRadius: BorderRadius.zero,
                )
              : null),
      // Use RepaintBoundary for complex children
      child: child != null && _shouldUseRepaintBoundary()
          ? RepaintBoundary(child: child!)
          : child,
    );
  }

  bool _shouldUseRepaintBoundary() {
    // Use RepaintBoundary for containers with complex decorations
    return decoration != null && decoration is BoxDecoration;
  }
}

/// Optimized animated container with debounced animations
class OptimizedAnimatedContainer extends StatefulWidget {
  final Widget? child;
  final double? width;
  final double? height;
  final Color? color;
  final Decoration? decoration;
  final Duration duration;
  final Curve curve;

  const OptimizedAnimatedContainer({
    Key? key,
    this.child,
    this.width,
    this.height,
    this.color,
    this.decoration,
    this.duration = const Duration(milliseconds: 200),
    this.curve = Curves.easeInOut,
  }) : super(key: key);

  @override
  State<OptimizedAnimatedContainer> createState() =>
      _OptimizedAnimatedContainerState();
}

class _OptimizedAnimatedContainerState
    extends State<OptimizedAnimatedContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
  }

  @override
  void didUpdateWidget(OptimizedAnimatedContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Debounce animation updates
    ComprehensivePerformanceManager.debounceOperation(() {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: widget.duration,
      curve: widget.curve,
      width: widget.width,
      height: widget.height,
      decoration: widget.decoration ??
          (widget.color != null
              ? BoxDecoration(color: widget.color)
              : null),
      child: widget.child,
    );
  }
}

/// Optimized gesture detector with debounced callbacks
class OptimizedGestureDetector extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final Duration debounceDuration;

  const OptimizedGestureDetector({
    Key? key,
    required this.child,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.debounceDuration = const Duration(milliseconds: 200),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap != null
          ? () => ComprehensivePerformanceManager.debounceOperation(
                onTap!,
                delay: debounceDuration,
              )
          : null,
      onDoubleTap: onDoubleTap != null
          ? () => ComprehensivePerformanceManager.debounceOperation(
                onDoubleTap!,
                delay: debounceDuration,
              )
          : null,
      onLongPress: onLongPress != null
          ? () => ComprehensivePerformanceManager.debounceOperation(
                onLongPress!,
                delay: debounceDuration,
              )
          : null,
      behavior: HitTestBehavior.opaque,
      child: child,
    );
  }
}

/// Optimized Obx widget that reduces rebuilds
class OptimizedObx<T> extends StatelessWidget {
  final Widget Function() builder;
  final String? debugLabel;

  const OptimizedObx(
    this.builder, {
    Key? key,
    this.debugLabel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Use RepaintBoundary to isolate repaints
      return RepaintBoundary(
        child: builder(),
      );
    });
  }
}

/// Optimized GetBuilder with performance improvements
class OptimizedGetBuilder<T extends GetxController> extends StatelessWidget {
  final Widget Function(T) builder;
  final bool global;
  final Object? id;
  final String? tag;
  final bool autoRemove;

  const OptimizedGetBuilder({
    Key? key,
    required this.builder,
    this.global = true,
    this.id,
    this.tag,
    this.autoRemove = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<T>(
      global: global,
      id: id,
      tag: tag,
      autoRemove: autoRemove,
      builder: (controller) {
        // Use RepaintBoundary to isolate repaints
        return RepaintBoundary(
          child: builder(controller),
        );
      },
    );
  }
}

/// Optimized circular progress indicator
class OptimizedCircularProgressIndicator extends StatelessWidget {
  final double? value;
  final Color? color;
  final double strokeWidth;
  final double size;

  const OptimizedCircularProgressIndicator({
    Key? key,
    this.value,
    this.color,
    this.strokeWidth = 4.0,
    this.size = 20.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        value: value,
        strokeWidth: strokeWidth,
        color: color,
        // Optimize for better performance
        backgroundColor: color?.withValues(alpha: 0.1),
      ),
    );
  }
}

/// Optimized card widget with performance improvements
class OptimizedCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Color? color;
  final double elevation;
  final BorderRadius? borderRadius;

  const OptimizedCard({
    Key? key,
    required this.child,
    this.margin,
    this.padding,
    this.color,
    this.elevation = 2.0,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: color ?? Theme.of(context).cardColor,
        borderRadius: borderRadius ?? BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: elevation * 2,
            offset: Offset(0, elevation),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(12.0),
        child: Padding(
          padding: padding ?? const EdgeInsets.all(16.0),
          child: child,
        ),
      ),
    );
  }
}

/// Mixin for StatefulWidgets to get performance optimizations
mixin PerformanceOptimizedStateMixin<T extends StatefulWidget> on State<T> {
  /// Use this instead of setState for debounced updates
  void setStateOptimized(VoidCallback fn) {
    ComprehensivePerformanceManager.debounceOperation(() {
      if (mounted) {
        setState(fn);
      }
    });
  }

  /// Check if expensive operation should be performed
  bool shouldPerformExpensiveOperation() {
    return WidgetsBinding.instance.lifecycleState == AppLifecycleState.resumed;
  }

  /// Defer expensive operations to next frame
  void deferExpensiveOperation(VoidCallback operation) {
    ComprehensivePerformanceManager.deferToNextFrame(operation);
  }
}