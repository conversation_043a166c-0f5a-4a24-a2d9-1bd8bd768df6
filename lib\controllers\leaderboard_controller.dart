import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';

class LeaderboardController extends GetxController {
  RxInt dailyHearts = 0.obs;
  RxInt defaultsHearts = 0.obs;

  final FirebaseFirestore firestore = FirebaseFirestore.instance;

  @override
  void onInit() {
    super.onInit();
    fetchHeartsValue();
  }

  Future<void> fetchHeartsValue() async {
    try {
      QuerySnapshot snapshot = await firestore.collection('hearts').get();

      if (snapshot.docs.isNotEmpty) {
        final data = snapshot.docs.first.data() as Map<String, dynamic>;

        dailyHearts.value = data['dailyHearts'] ?? 0;
        defaultsHearts.value = data['defaultHearts'] ?? 0;
      }
    } catch (e) {
      //
    }
  }
}
