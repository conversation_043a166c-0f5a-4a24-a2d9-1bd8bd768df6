// ignore_for_file: unused_element

import 'package:bibl/models/question_model.dart';
import 'package:bibl/res/style.dart';
import 'package:flutter/material.dart';

class QuestionWidget extends StatefulWidget {
  final QuestionModel question;
  final bool isAnswered;
  final int? selectedOptionIndexx;
  final int? totalQuestions;

  final Function({required String option, required int selectedIndex})
      onOptionSelected; // New callback function
  const QuestionWidget({
    Key? key,
    required this.question,
    required this.onOptionSelected,
    required this.isAnswered,
    this.selectedOptionIndexx,
    this.totalQuestions,
  }) : super(key: key);

  @override
  State<QuestionWidget> createState() => QuestionWidgetState();
}

class QuestionWidgetState extends State<QuestionWidget> {
  int? selectedOptionIndex;

  @override
  void initState() {
    super.initState();
    // Initialize selectedOptionIndex with the initial value of selectedOptionIndexx

    selectedOptionIndex = widget.selectedOptionIndexx;
  }

  @override
  void didUpdateWidget(covariant QuestionWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update selectedOptionIndex when selectedOptionIndexx changes
    if (widget.selectedOptionIndexx != null) {
      selectedOptionIndex = widget.selectedOptionIndexx;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.totalQuestions != null
              ? Column(
                  children: [
                    const SizedBox(height: 10),
                    Txt(
                      txt:
                          'PITANJE ${widget.question.qsNo} OD ${widget.totalQuestions}',
                      fontSize: 14,
                      fontColor: grey2Color,
                    ),
                    const SizedBox(height: 10),
                  ],
                )
              : const SizedBox.shrink(),
          const SizedBox(height: 10),
          Txt(
            txt: widget.question.question ?? '',
            fontSize: 16,
            maxLines: 10,
          ),
          const SizedBox(height: 20),
          questionOptionsWidget(),
        ]);
  }

  Column questionOptionsWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < widget.question.options!.length; i++)
          Column(
            children: [
              GestureDetector(
                onTap: widget.isAnswered
                    ? null
                    : () {
                        if (mounted) {
                          setState(() {
                            selectedOptionIndex = i;
                          });
                        }

                        // Notify the parent widget about the selected option
                        widget.onOptionSelected(
                            option: widget.question.options![i],
                            selectedIndex: i);
                      },
                child: Container(
                  decoration: BoxDecoration(
                    color: _getOptionColor(i),
                    border: Border.all(
                      color: _getOptionBorderColor(i),
                    ),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  height: 58,
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(3.0),
                            child: CircleAvatar(
                              backgroundColor: _getAnswerCircularBorderColor(i),
                              maxRadius: 9,
                              child: const CircleAvatar(
                                backgroundColor: Colors.white,
                                maxRadius: 3,
                              ),
                            ),
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Txt(
                            txt:
                                "${String.fromCharCode(65 + i)}. ", // Convert index to alphabet
                            fontSize: 14,
                            maxLines: 5,
                            fontWeight: FontWeight.normal,
                          ),
                          Expanded(
                            child: Txt(
                              txt: widget.question.options![i],
                              fontSize: 14,
                              maxLines: 5,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
      ],
    );
  }

  Color _getOptionColor(int optionIndex) {
    // Check if the answer is correct or if it's the selected one (right or wrong)
    if (widget.isAnswered) {
      if (widget.question.options![optionIndex] ==
          widget.question.correctOption) {
        return correctAnswerColor; // Highlight the correct answer in green
      } else if (selectedOptionIndex == optionIndex) {
        return wrongAnswerColor; // Highlight the wrong answer in red
      }
    }
    return Colors.transparent; // Default color for unselected options
  }

  Color _getOptionBorderColor(int optionIndex) {
    // Check if the border should highlight the correct or wrong answer
    if (widget.isAnswered) {
      if (widget.question.options![optionIndex] ==
          widget.question.correctOption) {
        return correctAnswerBorderColor; // Correct answer gets green border
      } else if (selectedOptionIndex == optionIndex) {
        return wrongAnswerBorderColor; // Wrong answer gets red border
      }
    }
    return greyishColor; // Default border color for other options
  }

  Color _getAnswerCircularBorderColor(int optionIndex) {
    if (widget.isAnswered) {
      if (widget.question.options![optionIndex] ==
          widget.question.correctOption) {
        return correctAnswerCircularBorderColor; // Correct answer gets green border
      } else if (selectedOptionIndex == optionIndex) {
        return wrongAnswerCircularBorderColor; // Wrong answer gets red border
      }
    }
    return Colors.grey; // Use the default color for unselected options
  }
}

class AnswersPageQuestionWidget extends StatefulWidget {
  final QuestionModel question;

  final List<String?> selectedAnswers;
  final int? totalQuestions;

  const AnswersPageQuestionWidget(
      {super.key,
      required this.question,
      required this.selectedAnswers,
      this.totalQuestions});

  @override
  State<AnswersPageQuestionWidget> createState() =>
      _AnswersPageQuestionWidgetState();
}

class _AnswersPageQuestionWidgetState extends State<AnswersPageQuestionWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.totalQuestions != null
              ? Column(
                  children: [
                    const SizedBox(height: 10),
                    Txt(
                      txt:
                          'PITANJE ${widget.question.qsNo} OD ${widget.totalQuestions}',
                      fontSize: 14,
                      fontColor: grey2Color,
                    ),
                    const SizedBox(height: 10),
                  ],
                )
              : const SizedBox.shrink(),
          const SizedBox(height: 10),
          Txt(
            txt: widget.question.question ?? '',
            fontSize: 16,
            maxLines: 10,
          ),
          const SizedBox(height: 20),
          questionOptionsWidget(),
          // const Column(
          //   children: [
          //     SizedBox(height: 10),
          //     Row(
          //       children: [
          //         Txt(
          //           txt: 'Objašnjenje',
          //           fontSize: 14,
          //           fontColor: grey2Color,
          //         ),
          //       ],
          //     ),
          //     SizedBox(height: 10),
          //     Txt(
          //       txt:
          //           'Sadio Mané has scored the fastest hat-trick in Premier League history in just two minutes and 56 seconds against Aston Villa on Saturday.',
          //       fontSize: 12,
          //       fontWeight: FontWeight.normal,
          //       maxLines: 1000,
          //       fontColor: grey2Color,
          //     ),
          //   ],
          // )
        ]);
  }

  Column questionOptionsWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < widget.question.options!.length; i++)
          Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: _getOptionColor(i),
                  border: Border.all(
                    color: _getOptionBorderColor(i),
                  ),
                  borderRadius: BorderRadius.circular(50),
                ),
                height: 58,
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(3.0),
                          child: CircleAvatar(
                            backgroundColor: _getAnswerCircularBorderColor(i),
                            maxRadius: 9,
                            child: const CircleAvatar(
                              backgroundColor: Colors.white,
                              maxRadius: 3,
                            ),
                          ),
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        Txt(
                          txt:
                              "${String.fromCharCode(65 + i)}. ", // Convert index to alphabet
                          fontSize: 14,
                          maxLines: 5,
                          fontWeight: FontWeight.normal,
                        ),
                        Expanded(
                          child: Txt(
                            txt: widget.question.options![i],
                            fontSize: 14,
                            maxLines: 5,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
      ],
    );
  }

  Color _getOptionColor(int optionIndex) {
    // If this option is the correct answer
    if (widget.question.options![optionIndex] ==
        widget.question.correctOption) {
      return correctAnswerColor; // green color
    }

    // If the user selected this option but it's incorrect
    if (widget.selectedAnswers
            .contains(widget.question.options![optionIndex]) &&
        widget.question.options![optionIndex] !=
            widget.question.correctOption) {
      return wrongAnswerColor; // red color
    }

    return Colors.transparent; // transparent for all other options
  }

  Color _getOptionBorderColor(int optionIndex) {
    // If this option is the correct answer
    if (widget.question.options![optionIndex] ==
        widget.question.correctOption) {
      return correctAnswerBorderColor; // green border color
    }

    // If the user selected this option but it's incorrect
    if (widget.selectedAnswers
            .contains(widget.question.options![optionIndex]) &&
        widget.question.options![optionIndex] !=
            widget.question.correctOption) {
      return wrongAnswerBorderColor; // red border color
    }

    return greyishColor; // greyish border for all other options
  }

  Color _getAnswerCircularBorderColor(int optionIndex) {
    // If this option is the correct answer
    if (widget.question.options![optionIndex] ==
        widget.question.correctOption) {
      return correctAnswerCircularBorderColor; // green circular color
    }

    // If the user selected this option but it's incorrect
    if (widget.selectedAnswers
            .contains(widget.question.options![optionIndex]) &&
        widget.question.options![optionIndex] !=
            widget.question.correctOption) {
      return wrongAnswerCircularBorderColor; // red circular color
    }

    return Colors.grey; // grey color for all other options
  }
}

class ShuffleQuizQuestionWidget extends StatefulWidget {
  final ShuffleQuizQuestionModel question;
  final bool? selectedAnswer;
  final int? totalQuestions;

  const ShuffleQuizQuestionWidget({
    super.key,
    required this.question,
    required this.selectedAnswer,
    this.totalQuestions,
  });

  @override
  State<ShuffleQuizQuestionWidget> createState() =>
      _ShuffleQuizQuestionWidgetState();
}

class _ShuffleQuizQuestionWidgetState extends State<ShuffleQuizQuestionWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.totalQuestions != null
            ? Column(
                children: [
                  const SizedBox(height: 10),
                  Txt(
                    txt:
                        'PITANJE ${widget.question.qsNo} OD ${widget.totalQuestions}',
                    fontSize: 14,
                    fontColor: grey2Color,
                  ),
                  const SizedBox(height: 10),
                ],
              )
            : const SizedBox.shrink(),
        const SizedBox(height: 10),
        Txt(
          txt: widget.question.question ?? '',
          fontSize: 16,
          maxLines: 10,
        ),
        const SizedBox(height: 20),
        questionOptionsWidget(),
        // const Column(
        //   children: [
        //     SizedBox(height: 10),
        //     Row(
        //       children: [
        //         Txt(
        //           txt: 'Objašnjenje',
        //           fontSize: 14,
        //           fontColor: grey2Color,
        //         ),
        //       ],
        //     ),
        //     SizedBox(height: 10),
        //     Txt(
        //       txt:
        //           'Sadio Mané has scored the fastest hat-trick in Premier League history in just two minutes and 56 seconds against Aston Villa on Saturday.',
        //       fontSize: 12,
        //       fontWeight: FontWeight.normal,
        //       maxLines: 1000,
        //       fontColor: grey2Color,
        //     ),
        //   ],
        // ),
      ],
    );
  }

  Column questionOptionsWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // True Option (A)
        optionContainer("A: True", true),
        const SizedBox(height: 16),

        // False Option (B)
        optionContainer("B: False", false),
      ],
    );
  }

  Widget optionContainer(String label, bool optionValue) {
    return Container(
      decoration: BoxDecoration(
        color: _getOptionColor(optionValue),
        border: Border.all(
          color: _getOptionBorderColor(optionValue),
        ),
        borderRadius: BorderRadius.circular(50),
      ),
      height: 58,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(3.0),
                child: CircleAvatar(
                  backgroundColor: _getAnswerCircularBorderColor(optionValue),
                  maxRadius: 9,
                  child: const CircleAvatar(
                    backgroundColor: Colors.white,
                    maxRadius: 3,
                  ),
                ),
              ),
              const SizedBox(width: 5),
              Txt(
                txt: label,
                fontSize: 14,
                maxLines: 5,
                fontWeight: FontWeight.normal,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getOptionColor(bool optionValue) {
    // Case 1: User has selected an answer
    if (widget.selectedAnswer != null) {
      // Case 1.1: User's selected answer is correct
      if (widget.selectedAnswer == widget.question.correctOption) {
        return optionValue == widget.selectedAnswer
            ? correctAnswerColor // Selected correct answer
            : Colors.transparent; // Other options stay transparent
      }
      // Case 1.2: User's selected answer is incorrect
      else {
        return optionValue == widget.selectedAnswer
            ? wrongAnswerColor // Selected incorrect answer
            : (optionValue == widget.question.correctOption
                ? correctAnswerColor // Show correct answer in green
                : Colors.transparent); // Unselected options stay transparent
      }
    }

    // Case 2: No answer selected, return transparent for all
    return Colors.transparent;
  }

  Color _getOptionBorderColor(bool optionValue) {
    if (widget.selectedAnswer != null) {
      if (widget.selectedAnswer == widget.question.correctOption) {
        return optionValue == widget.selectedAnswer
            ? correctAnswerBorderColor
            : greyishColor;
      } else {
        return optionValue == widget.selectedAnswer
            ? wrongAnswerBorderColor
            : (optionValue == widget.question.correctOption
                ? correctAnswerBorderColor
                : greyishColor);
      }
    }

    return greyishColor;
  }

  Color _getAnswerCircularBorderColor(bool optionValue) {
    if (widget.selectedAnswer != null) {
      if (widget.selectedAnswer == widget.question.correctOption) {
        return optionValue == widget.selectedAnswer
            ? correctAnswerCircularBorderColor
            : Colors.grey;
      } else {
        return optionValue == widget.selectedAnswer
            ? wrongAnswerCircularBorderColor
            : (optionValue == widget.question.correctOption
                ? correctAnswerCircularBorderColor
                : Colors.grey);
      }
    }

    return Colors.grey;
  }
}
