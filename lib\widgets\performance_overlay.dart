import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

/// Performance monitoring overlay for debugging
class PerformanceOverlay extends StatefulWidget {
  final Widget child;
  final bool enabled;

  const PerformanceOverlay({
    Key? key,
    required this.child,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<PerformanceOverlay> createState() => _PerformanceOverlayState();
}

class _PerformanceOverlayState extends State<PerformanceOverlay> {
  double _currentFps = 60.0;
  int _frameCount = 0;
  Duration _totalElapsed = Duration.zero;
  Duration _lastTimestamp = Duration.zero;

  @override
  void initState() {
    super.initState();
    if (widget.enabled && kDebugMode) {
      _startMonitoring();
    }
  }

  void _startMonitoring() {
    SchedulerBinding.instance.addPersistentFrameCallback((timeStamp) {
      if (!mounted) return;

      if (_lastTimestamp == Duration.zero) {
        _lastTimestamp = timeStamp;
        return;
      }

      _frameCount++;
      _totalElapsed += timeStamp - _lastTimestamp;
      _lastTimestamp = timeStamp;

      // Calculate FPS every 30 frames
      if (_frameCount >= 30) {
        final fps = (_frameCount * 1000000) / _totalElapsed.inMicroseconds;
        setState(() {
          _currentFps = fps;
        });
        _frameCount = 0;
        _totalElapsed = Duration.zero;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled || !kDebugMode) {
      return widget.child;
    }

    return Stack(
      children: [
        widget.child,
        Positioned(
          top: 50,
          right: 10,
          child: IgnorePointer(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getFpsColor(),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 4,
                  ),
                ],
              ),
              child: Text(
                '${_currentFps.toStringAsFixed(1)} FPS',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getFpsColor() {
    if (_currentFps >= 55) {
      return Colors.green;
    } else if (_currentFps >= 45) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
